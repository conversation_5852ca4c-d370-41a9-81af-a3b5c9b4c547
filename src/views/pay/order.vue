<template>
  <div class="mod-pay__order">
    <el-form :inline="true" :model="state.dataForm" @keyup.enter="state.getDataList()">
      <el-form-item>
        <el-input v-model="state.dataForm.orderId" :placeholder="$t('order.orderId')" clearable></el-input>
      </el-form-item>
      <el-form-item>
        <el-input v-model="state.dataForm.userId" :placeholder="$t('order.userId')" clearable></el-input>
      </el-form-item>
      <el-form-item>
        <ren-select v-model="state.dataForm.status" dict-type="order_status" :placeholder="$t('order.status')"></ren-select>
      </el-form-item>
      <el-form-item>
        <el-button @click="state.getDataList()">{{ $t("query") }}</el-button>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="addOrUpdateHandle()">{{ $t("add") }}</el-button>
      </el-form-item>
    </el-form>
    <el-table v-loading="state.dataListLoading" :data="state.dataList" border @selection-change="state.dataListSelectionChangeHandle" style="width: 100%">
      <el-table-column type="selection" header-align="center" align="center" width="50"></el-table-column>
      <el-table-column prop="orderId" :label="$t('order.orderId')" header-align="center" align="center"></el-table-column>
      <el-table-column prop="productName" :label="$t('order.productName')" header-align="center" align="center"></el-table-column>
      <el-table-column prop="payAmount" :label="$t('order.payAmount')" header-align="center" align="center"></el-table-column>
      <el-table-column prop="status" :label="$t('order.status')" header-align="center" align="center">
        <template v-slot="scope">
          {{ state.getDictLabel("order_status", scope.row.status) }}
        </template>
      </el-table-column>
      <el-table-column prop="payAt" :label="$t('order.payAt')" header-align="center" align="center"></el-table-column>
      <el-table-column prop="createDate" :label="$t('order.createDate')" header-align="center" align="center"></el-table-column>
      <el-table-column :label="$t('handle')" fixed="right" header-align="center" align="center" width="150">
        <template v-slot="scope">
          <el-button v-if="scope.row.status === 0" type="primary" link @click="payHandle(scope.row.orderId)">{{ $t("order.pay") }}</el-button>
          <el-button v-if="scope.row.status === 0" type="primary" link @click="state.deleteHandle(scope.row.id)">{{ $t("delete") }}</el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination :current-page="state.page" :page-sizes="[10, 20, 50, 100]" :page-size="state.limit" :total="state.total" layout="total, sizes, prev, pager, next, jumper" @size-change="state.pageSizeChangeHandle" @current-change="state.pageCurrentChangeHandle"> </el-pagination>
    <!-- 弹窗, 新增 / 修改 -->
    <add-or-update ref="addOrUpdateRef" @refreshDataList="state.getDataList"></add-or-update>
  </div>
</template>

<script lang="ts" setup>
import useView from "@/hooks/useView";
import { reactive, ref, toRefs } from "vue";
import AddOrUpdate from "./order-add-or-update.vue";
import app from "@/constants/app";

const view = reactive({
  getDataListURL: "/pay/order/page",
  getDataListIsPage: true,
  deleteURL: "/pay/order",
  deleteIsBatch: true,
  dataForm: {
    orderId: "",
    status: "",
    userId: ""
  }
});

const state = reactive({ ...useView(view), ...toRefs(view) });

const payHandle = (orderId: string) => {
  window.open(`${app.api}/pay/alipay/webPay?orderId=` + orderId);
};

const addOrUpdateRef = ref();
const addOrUpdateHandle = () => {
  addOrUpdateRef.value.init();
};
</script>
