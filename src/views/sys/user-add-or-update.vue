<template>
  <el-dialog v-model="visible" :title="!dataForm.id ? $t('add') : $t('update')" :close-on-click-modal="false" :close-on-press-escape="false">
    <el-form :model="dataForm" :rules="rules" ref="dataFormRef" @keyup.enter="dataFormSubmitHandle()" label-width="120px">
      <el-form-item prop="username" :label="$t('user.username')">
        <el-input v-model="dataForm.username" :placeholder="$t('user.username')"></el-input>
      </el-form-item>
      <el-form-item prop="deptName" :label="$t('user.deptName')">
        <ren-dept-tree v-model="dataForm.deptId" :placeholder="$t('dept.title')" v-model:deptName="dataForm.deptName"></ren-dept-tree>
      </el-form-item>
      <el-form-item prop="password" :label="$t('user.password')" :class="{ 'is-required': !dataForm.id }">
        <el-input v-model="dataForm.password" type="password" :placeholder="$t('user.password')"></el-input>
      </el-form-item>
      <el-form-item prop="confirmPassword" :label="$t('user.confirmPassword')" :class="{ 'is-required': !dataForm.id }">
        <el-input v-model="dataForm.confirmPassword" type="password" :placeholder="$t('user.confirmPassword')"></el-input>
      </el-form-item>
      <el-form-item label="账号类型" prop="carType">
        <el-select v-model="dataForm.loginType" placeholder="请选择账号类型">
          <el-option
            v-for="(value, key) in loginTypes"
            :key="key"
            :label="value"
            :value="key"
            :selected-label="value"
          ></el-option>
        </el-select>
      </el-form-item>
<!--      <el-form-item prop="realName" :label="$t('user.realName')">-->
<!--        <el-input v-model="dataForm.realName" :placeholder="$t('user.realName')"></el-input>-->
<!--      </el-form-item>-->
<!--      <el-form-item prop="gender" :label="$t('user.gender')">-->
<!--        <ren-radio-group v-model="dataForm.gender" dict-type="gender"></ren-radio-group>-->
<!--      </el-form-item>-->
<!--      <el-form-item prop="email" :label="$t('user.email')">-->
<!--        <el-input v-model="dataForm.email" :placeholder="$t('user.email')"></el-input>-->
<!--      </el-form-item>-->
      <el-form-item prop="mobile" :label="$t('user.mobile')">
        <el-input v-model="dataForm.mobile" :placeholder="$t('user.mobile')"></el-input>
      </el-form-item>
      <el-form-item prop="roleIdList" :label="$t('user.roleIdList')" class="role-list">
        <el-select v-model="dataForm.roleIdList" multiple :placeholder="$t('user.roleIdList')">
          <el-option v-for="role in roleList" :key="role.id" :label="role.name" :value="role.id"></el-option>
        </el-select>
      </el-form-item>
<!--      <el-form-item prop="postIdList" :label="$t('user.postIdList')" class="role-list">-->
<!--        <el-select v-model="dataForm.postIdList" multiple :placeholder="$t('user.postIdList')">-->
<!--          <el-option v-for="post in postList" :key="post.id" :label="post.postName" :value="post.id"></el-option>-->
<!--        </el-select>-->
<!--      </el-form-item>-->
      <el-form-item prop="status" :label="$t('user.status')">
        <el-radio-group v-model="dataForm.status">
          <el-radio :label="0">{{ $t("user.status0") }}</el-radio>
          <el-radio :label="1">{{ $t("user.status1") }}</el-radio>
        </el-radio-group>
      </el-form-item>
    </el-form>
    <template v-slot:footer>
      <el-button @click="visible = false">{{ $t("cancel") }}</el-button>
      <el-button type="primary" @click="dataFormSubmitHandle()">{{ $t("confirm") }}</el-button>
    </template>
  </el-dialog>

  <!-- 验证信息弹窗 -->
  <el-dialog v-model="verificationVisible" title="验证码查看" :close-on-click-modal="false" :close-on-press-escape="false" width="500px">
    <div v-loading="verificationLoading">
      <el-descriptions v-if="verificationData" :column="1" border size="large">
        <el-descriptions-item label="用户名">
          <span>{{ verificationData.userName || '暂无数据' }}</span>
        </el-descriptions-item>
        <el-descriptions-item label="手机号">
          <span>{{ verificationData.userPhone || '暂无数据' }}</span>
        </el-descriptions-item>
        <el-descriptions-item label="验证码">
          <el-tag v-if="verificationData.verification && verificationData.verification !== '未查询到验证码'"
                  type="success" size="large">
            {{ verificationData.verification }}
          </el-tag>
          <el-tag v-else type="info" size="large">
            {{ verificationData.verification || '未查询到验证码' }}
          </el-tag>
        </el-descriptions-item>
      </el-descriptions>
      <el-empty v-else description="暂无验证数据" />
    </div>
    <template v-slot:footer>
      <el-button @click="verificationVisible = false">{{ $t("close") }}</el-button>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { reactive, ref } from "vue";
import baseService from "@/service/baseService";
import { isEmail, isMobile } from "@/utils/utils";
import { IObject } from "@/types/interface";
import { useI18n } from "vue-i18n";
import { ElMessage } from "element-plus";
const { t } = useI18n();
const emit = defineEmits(["refreshDataList"]);

const visible = ref(false);
const roleList = ref<any[]>([]);
const postList = ref<any[]>([]);
const dataFormRef = ref();

// 验证信息相关
const verificationVisible = ref(false);
const verificationLoading = ref(false);
const verificationData = ref<any>(null);

const dataForm = reactive({
  id: "",
  username: "",
  deptId: "",
  deptName: "",
  password: "",
  confirmPassword: "",
  realName: "",
  gender: 0,
  email: "",
  mobile: "",
  loginType:"0",
  roleIdList: [] as IObject[],
  postIdList: [] as IObject[],
  status: 1
});

const validatePassword = (rule: any, value: string, callback: (e?: Error) => any): any => {
  if (!dataForm.id && !/\S/.test(value)) {
    return callback(new Error(t("validate.required")));
  }
  callback();
};
const validateConfirmPassword = (rule: any, value: string, callback: (e?: Error) => any): any => {
  if (!dataForm.id && !/\S/.test(value)) {
    return callback(new Error(t("validate.required")));
  }
  if (dataForm.password !== value) {
    return callback(new Error(t("user.validate.confirmPassword")));
  }
  callback();
};
const validateEmail = (rule: any, value: string, callback: (e?: Error) => any): any => {
  if (value && !isEmail(value)) {
    return callback(new Error(t("validate.format", { attr: t("user.email") })));
  }
  callback();
};
const validateMobile = (rule: any, value: string, callback: (e?: Error) => any): any => {
  if (value && !isMobile(value)) {
    return callback(new Error(t("validate.format", { attr: t("user.mobile") })));
  }
  callback();
};
const rules = ref({
  username: [{ required: true, message: t("validate.required"), trigger: "blur" }],
  deptName: [{ required: true, message: t("validate.required"), trigger: "change" }],
  password: [{ validator: validatePassword, trigger: "blur" }],
  confirmPassword: [{ validator: validateConfirmPassword, trigger: "blur" }],
  realName: [{ required: true, message: t("validate.required"), trigger: "blur" }],
  email: [{ validator: validateEmail, trigger: "blur" }],
  mobile: [{ validator: validateMobile, trigger: "blur" }]
});

// 定义车辆类别映射
const loginTypes = {
  0: '后台',
  1: '公众号',
  // 添加其他支付方式...
};

const init = (id?: number) => {
  visible.value = true;
  dataForm.id = "";
  dataForm.deptId = "";

  // 重置表单数据
  if (dataFormRef.value) {
    dataFormRef.value.resetFields();
  }

  Promise.all([getRoleList(), getPostList()]).then(() => {
    if (id) {
      getInfo(id);
    }
  });
};

// 获取角色列表
const getRoleList = () => {
  return baseService.get("/sys/role/list").then((res) => {
    roleList.value = res.data;
  });
};

// 获取岗位列表
const getPostList = () => {
  return baseService.get("/sys/post/list").then((res) => {
    postList.value = res.data;
  });
};

// 获取信息
const getInfo = (id: number) => {
  baseService.get(`/sys/user/${id}`).then((res) => {
    Object.assign(dataForm, res.data);
  });
};

// 表单提交
const dataFormSubmitHandle = () => {
  dataFormRef.value.validate((valid: boolean) => {
    if (!valid) {
      return false;
    }
    (!dataForm.id ? baseService.post : baseService.put)("/sys/user", {
      ...dataForm,
      roleIdList: [...dataForm.roleIdList]
    }).then(() => {
      ElMessage.success({
        message: t("prompt.success"),
        duration: 500,
        onClose: () => {
          visible.value = false;
          emit("refreshDataList");
        }
      });
    });
  });
};

// 验证方法
const verification = (id?: number) => {
  if (!id) {
    ElMessage.warning("请选择要验证的用户");
    return;
  }

  verificationVisible.value = true;
  verificationLoading.value = true;
  verificationData.value = null;

  // 调用后端验证接口
  baseService.get(`/sys/user/${id}`)
    .then((res) => {
      if (res.code === 0) {
        verificationData.value = res.data;
        ElMessage.success("验证码查询成功");
      } else {
        ElMessage.error(res.msg || "获取验证码失败");
      }
    })
    .catch((error) => {
      console.error("Verification error:", error);
      ElMessage.error("获取验证码失败");
    })
    .finally(() => {
      verificationLoading.value = false;
    });
};

defineExpose({
  init,
  verification
});
</script>

<style lang="less" scoped>
.mod-sys__user {
  .role-list {
    .el-select {
      width: 100%;
    }
  }
}
</style>
