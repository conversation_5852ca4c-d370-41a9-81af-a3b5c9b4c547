<template>
  <el-dialog v-model="visible" title="代码生成参数配置" :close-on-click-modal="false" :close-on-press-escape="false">
    <el-form :model="dataForm" :rules="rules" ref="dataFormRef" @keyup.enter="dataFormSubmitHandle()" label-width="120px">
      <el-form-item label="默认包名" prop="packageName">
        <el-input v-model="dataForm.packageName" placeholder="默认包名"></el-input>
      </el-form-item>
      <el-form-item label="默认版本号" prop="version">
        <el-input v-model="dataForm.version" placeholder="默认版本号"></el-input>
      </el-form-item>
      <el-form-item label="默认作者" prop="author">
        <el-input v-model="dataForm.author" placeholder="默认作者"></el-input>
      </el-form-item>
      <el-form-item label="作者邮箱" prop="email">
        <el-input v-model="dataForm.email" placeholder="作者邮箱"></el-input>
      </el-form-item>
      <el-form-item label="后端生成路径" prop="backendPath">
        <el-input v-model="dataForm.backendPath" placeholder="后端生成路径"></el-input>
      </el-form-item>
      <el-form-item label="前端生成路径" prop="frontendPath">
        <el-input v-model="dataForm.frontendPath" placeholder="前端生成路径"></el-input>
      </el-form-item>
    </el-form>
    <template v-slot:footer>
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmitHandle()">确定</el-button>
    </template>
  </el-dialog>
</template>
<script lang="ts" setup>
import { reactive, ref } from "vue";
import baseService from "@/service/baseService";
import { useI18n } from "vue-i18n";
import { ElMessage } from "element-plus";
const { t } = useI18n();
const emit = defineEmits(["refreshDataList"]);

const visible = ref(false);
const dataFormRef = ref();

const dataForm = reactive({
  packageName: "",
  version: "",
  author: "",
  email: "",
  backendPath: "",
  frontendPath: ""
});

const rules = ref({
  packageName: [{ required: true, message: "必填项不能为空", trigger: "blur" }],
  version: [{ required: true, message: "必填项不能为空", trigger: "blur" }],
  author: [{ required: true, message: "必填项不能为空", trigger: "blur" }],
  backendPath: [{ required: true, message: "必填项不能为空", trigger: "blur" }],
  frontendPath: [{ required: true, message: "必填项不能为空", trigger: "blur" }]
});

const init = () => {
  visible.value = true;

  // 重置表单数据
  if (dataFormRef.value) {
    dataFormRef.value.resetFields();
  }

  getInfo();
};

// 获取信息
const getInfo = () => {
  baseService.get("/devtools/param/info").then((res) => {
    Object.assign(dataForm, res.data);
  });
};

// 表单提交
const dataFormSubmitHandle = () => {
  dataFormRef.value.validate((valid: boolean) => {
    if (!valid) {
      return false;
    }
    baseService.post("/devtools/param", dataForm).then(() => {
      ElMessage.success({
        message: t("prompt.success"),
        duration: 500,
        onClose: () => {
          visible.value = false;
          emit("refreshDataList");
        }
      });
    });
  });
};

defineExpose({
  init
});
</script>
