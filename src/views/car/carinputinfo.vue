<template>
  <div class="mod-dj__carinputinfo">
    <el-form :inline="true" :model="state.dataForm" @keyup.enter="state.getDataList()">
      <el-form-item>
        <el-select v-model="state.dataForm.carType" placeholder="车辆类别">
          <el-option
            v-for="(value, key) in carTypes"
            :key="key"
            :label="value"
            :value="key"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-input v-model="state.dataForm.carId" placeholder="车牌信息" clearable></el-input>
      </el-form-item>
      <el-form-item>
        <el-input v-model="state.dataForm.carUserName" placeholder="车主姓名" clearable></el-input>
      </el-form-item>
      <el-form-item>
        <el-input v-model="state.dataForm.carUserPhone" placeholder="车主电话" clearable></el-input>
      </el-form-item>
      <el-form-item>
        <el-input v-model="state.dataForm.schoolName" placeholder="驾校名称" clearable></el-input>
      </el-form-item>
      <el-form-item>
        <el-button @click="state.getDataList()">{{ $t("query") }}</el-button>
      </el-form-item>
      <el-form-item>
        <el-button type="info" @click="state.exportHandle()">{{ $t("export") }}</el-button>
      </el-form-item>
      <el-form-item>
        <el-button v-if="state.hasPermission('dj:carinputinfo:save')" type="primary" @click="addOrUpdateHandle()">{{ $t("add") }}</el-button>
      </el-form-item>
      <el-form-item>
        <el-button v-if="state.hasPermission('dj:carinputinfo:delete')" type="danger" @click="state.deleteHandle()">{{ $t("deleteBatch") }}</el-button>
      </el-form-item>
    </el-form>
    <el-table v-loading="state.dataListLoading" :data="state.dataList" border @selection-change="state.dataListSelectionChangeHandle" style="width: 100%">
      <el-table-column type="selection" header-align="center" align="center" width="50"></el-table-column>
      <el-table-column prop="carType" label="车辆类别" header-align="center" align="center" :formatter="formatCarType"></el-table-column>
      <el-table-column prop="carId" label="车牌信息" header-align="center" align="center"></el-table-column>
      <el-table-column prop="carStatus" label="车辆状态" header-align="center" align="center">
        <template v-slot="scope">
          <span :class="getStatusClass(scope.row.carStatus)">{{ getStatusText(scope.row.carStatus) }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="carUserName" label="车主姓名" header-align="center" align="center">
        <template v-slot="scope">
          {{ scope.row.carUserName === null || scope.row.carUserName ===  "" ? '无' : scope.row.carUserName }}
        </template>
      </el-table-column>
      <el-table-column prop="carUserPhone" label="车主电话" header-align="center" align="center">
        <template v-slot="scope">
          {{ scope.row.carUserPhone === null || scope.row.carUserPhone ===  "" ? '无' : scope.row.carUserPhone }}
        </template>
      </el-table-column>
      <el-table-column prop="schoolName" label="驾校名称" header-align="center" align="center">
        <template v-slot="scope">
          {{ scope.row.schoolName === null || scope.row.schoolName ===  "" ? '无' : scope.row.schoolName }}
        </template>
      </el-table-column>
<!--      <el-table-column prop="examinationRoomName" label="考场名称" header-align="center" align="center">-->
<!--        <template v-slot="scope">-->
<!--          {{ scope.row.examinationRoomName === null || scope.row.examinationRoomName ===  "" ? '无' : scope.row.examinationRoomName }}-->
<!--        </template>-->
<!--      </el-table-column>-->
      <el-table-column prop="createBy" label="创建人" header-align="center" align="center"></el-table-column>
      <el-table-column prop="createDate" label="创建时间" header-align="center" align="center"></el-table-column>
      <el-table-column prop="updateBy" label="更新人" header-align="center" align="center"></el-table-column>
      <el-table-column prop="updateDate" label="更新时间" header-align="center" align="center"></el-table-column>
      <el-table-column :label="$t('handle')" fixed="right" header-align="center" align="center" width="150">
        <template v-slot="scope">
          <el-button v-if="state.hasPermission('dj:carinputinfo:update')" type="primary" link @click="addOrUpdateHandle(scope.row.id)">{{ $t("update") }}</el-button>
          <el-button v-if="state.hasPermission('dj:carinputinfo:delete')" type="primary" link @click="state.deleteHandle(scope.row.id)">{{ $t("delete") }}</el-button>
          <el-button v-if="scope.row.carType === '1' && state.hasPermission('dj:carunbindrecord:record')" type="primary" link @click="unbindHandle(scope.row.id)">{{ $t("unbindingVehicle") }}</el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination :current-page="state.page" :page-sizes="[10, 20, 50, 100]" :page-size="state.limit" :total="state.total" layout="total, sizes, prev, pager, next, jumper" @size-change="state.pageSizeChangeHandle" @current-change="state.pageCurrentChangeHandle"> </el-pagination>
    <!-- 弹窗, 新增 / 修改 -->
    <add-or-update :key="addKey" ref="addOrUpdateRef" @refreshDataList="state.getDataList"></add-or-update>
    <unbind :key="unbindKey" ref="unbindRef" @refreshDataList="state.getDataList"></unbind>
  </div>
</template>

<script lang="ts" setup>
import useView from "@/hooks/useView";
import { nextTick, reactive, ref, toRefs, watch } from "vue";
import AddOrUpdate from "./carinputinfo-add-or-update.vue";
import unbind from "./carunbindrecord-add-or-update.vue";

const view = reactive({
  getDataListURL: "/dj/carinputinfo/page",
  getDataListIsPage: true,
  exportURL: "/dj/carinputinfo/export",
  deleteURL: "/dj/carinputinfo",
  deleteIsBatch: true,
  dataForm: {
    carId: "",
    carUserName: "",
    carUserPhone: "",
    schoolName: "",
    carType: "",
  }
});

const state = reactive({ ...useView(view), ...toRefs(view) });

// 定义车辆类别映射
const carTypes = {
  1: '教练车',
  2: '测试车',
  3: '带训车',
  // 添加其他支付方式...
};

// 定义状态类别映射
const carStatus = {
  0: '空闲中',
  1: '使用中',
  9: '已解绑',
};

// 获取状态文本
const getStatusText = (status: string | number) => {
  return carStatus[status] || '未知';
};

// 获取状态对应的CSS类名
const getStatusClass = (status: string | number) => {
  return `status-${status === '0' ? 'idle' : 'in-use'}`;
};

// 添加格式化方法
const formatCarType = (row, column, cellValue) => {
  return carTypes[cellValue] || '未知';
};

const addKey = ref(0);
const addOrUpdateRef = ref();
const unbindKey = ref(0);
const unbindRef = ref();
const addOrUpdateHandle = (id?: number) => {
  addKey.value++;
  nextTick(() => {
    addOrUpdateRef.value.init(id);
  });
};

const unbindHandle = (id?: number) => {
  unbindKey.value++;
  nextTick(() => {
    unbindRef.value.init(id);
  });
};
</script>
<style scoped>
.status-idle {
  color: green;
}

.status-in-use {
  color: red;
}
</style>
