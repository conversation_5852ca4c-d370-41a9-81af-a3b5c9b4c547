<template>
  <div class="mod-dj__paymentmethodcfg">
    <el-form :inline="true" :model="state.dataForm" @keyup.enter="state.getDataList()">
      <el-form-item>
        <el-select v-model="state.dataForm.paymentMethodType" placeholder="支付方式">
          <el-option
            v-for="(value, key) in paymentMethodTypes"
            :key="key"
            :label="value"
            :value="key"
          ></el-option>
        </el-select>
      </el-form-item>
<!--      <el-form-item>-->
<!--        <el-input v-model="state.dataForm.schoolName" placeholder="考场名称" clearable></el-input>-->
<!--      </el-form-item>-->
      <el-form-item>
        <el-button @click="state.getDataList()">{{ $t("query") }}</el-button>
      </el-form-item>
      <el-form-item>
        <el-button type="info" @click="state.exportHandle()">{{ $t("export") }}</el-button>
      </el-form-item>
      <el-form-item>
        <el-button v-if="state.hasPermission('dj:paymentmethodcfg:save')" type="primary" @click="addOrUpdateHandle()">{{ $t("add") }}</el-button>
      </el-form-item>
      <el-form-item>
        <el-button v-if="state.hasPermission('dj:paymentmethodcfg:delete')" type="danger" @click="state.deleteHandle()">{{ $t("deleteBatch") }}</el-button>
      </el-form-item>
    </el-form>
    <el-table v-loading="state.dataListLoading" :data="state.dataList" border @selection-change="state.dataListSelectionChangeHandle" style="width: 100%">
      <el-table-column type="selection" header-align="center" align="center" width="50"></el-table-column>
      <el-table-column prop="paymentMethodType" label="支付方式" header-align="center" align="center" :formatter="formatPaymentMethod"></el-table-column>
<!--      <el-table-column prop="schoolName" label="考场名称" header-align="center" align="center"></el-table-column>-->
      <el-table-column prop="createBy" label="创建人" header-align="center" align="center"></el-table-column>
      <el-table-column prop="createDate" label="创建时间" header-align="center" align="center"></el-table-column>
      <el-table-column prop="updateBy" label="更新人" header-align="center" align="center"></el-table-column>
      <el-table-column prop="updateDate" label="更新时间" header-align="center" align="center"></el-table-column>
      <el-table-column :label="$t('handle')" fixed="right" header-align="center" align="center" width="150">
        <template v-slot="scope">
          <el-button v-if="state.hasPermission('dj:paymentmethodcfg:update')" type="primary" link @click="addOrUpdateHandle(scope.row.id)">{{ $t("update") }}</el-button>
          <el-button v-if="state.hasPermission('dj:paymentmethodcfg:delete')" type="primary" link @click="state.deleteHandle(scope.row.id)">{{ $t("delete") }}</el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination :current-page="state.page" :page-sizes="[10, 20, 50, 100]" :page-size="state.limit" :total="state.total" layout="total, sizes, prev, pager, next, jumper" @size-change="state.pageSizeChangeHandle" @current-change="state.pageCurrentChangeHandle"> </el-pagination>
    <!-- 弹窗, 新增 / 修改 -->
    <add-or-update :key="addKey" ref="addOrUpdateRef" @refreshDataList="state.getDataList"></add-or-update>
  </div>
</template>

<script lang="ts" setup>
import useView from "@/hooks/useView";
import { computed, nextTick, reactive, ref, toRefs, watch } from "vue";
import AddOrUpdate from "./paymentmethodcfg-add-or-update.vue";

const view = reactive({
  getDataListURL: "/dj/paymentmethodcfg/page",
  getDataListIsPage: true,
  exportURL: "/dj/paymentmethodcfg/export",
  deleteURL: "/dj/paymentmethodcfg",
  deleteIsBatch: true,
  dataForm: {
  }
});

// 定义支付方式映射
const paymentMethodTypes = {
  1: '微信',
  2: '支付宝',
  // 添加其他支付方式...
};


// 添加格式化方法
const formatPaymentMethod = (row, column, cellValue) => {
  return paymentMethodTypes[cellValue] || '未知';
};

const state = reactive({ ...useView(view), ...toRefs(view) });

const tableRef = ref(null);
const addKey = ref(0);
const addOrUpdateRef = ref();


// 定义一个计算属性来获取所有 deptId
const allDeptIds = computed(() => {
  return state.dataList.map(item => item.deptId);
});
const addOrUpdateHandle = (id?: number) => {
  addKey.value++;
  nextTick(() => {
    const deptIds = allDeptIds.value;
    console.log("所有驾校id"+deptIds)
    addOrUpdateRef.value.init(id,deptIds);
  });
};
</script>
