<template>
  <el-dialog v-model="visible" :title="!dataForm.id ? $t('add') : $t('update')" :close-on-click-modal="false" :close-on-press-escape="false">
    <el-form :model="dataForm" :rules="rules" ref="dataFormRef" @keyup.enter="dataFormSubmitHandle()" label-width="120px">
      <el-form-item label="主键" prop="id">
        <el-input v-model="dataForm.id" placeholder="主键"></el-input>
      </el-form-item>
      <el-form-item label="预约设置表ID" prop="bookingCfgId">
        <el-input v-model="dataForm.bookingCfgId" placeholder="预约设置表ID"></el-input>
      </el-form-item>
      <el-form-item label="教练用户ID" prop="carUserId">
        <el-input v-model="dataForm.carUserId" placeholder="教练用户ID"></el-input>
      </el-form-item>
      <el-form-item label="教练用户姓名" prop="carUserName">
        <el-input v-model="dataForm.carUserName" placeholder="教练用户姓名"></el-input>
      </el-form-item>
      <el-form-item label="教练用户手机" prop="carUserPhone">
        <el-input v-model="dataForm.carUserPhone" placeholder="教练用户手机"></el-input>
      </el-form-item>
      <el-form-item label="预约用户状态;0：未使用  1：已使用" prop="userStatus">
        <el-input v-model="dataForm.userStatus" placeholder="预约用户状态;0：未使用  1：已使用"></el-input>
      </el-form-item>
      <el-form-item label="公司id" prop="deptId">
        <el-input v-model="dataForm.deptId" placeholder="公司id"></el-input>
      </el-form-item>
      <el-form-item label="公司用户id" prop="companyUserId">
        <el-input v-model="dataForm.companyUserId" placeholder="公司用户id"></el-input>
      </el-form-item>
      <el-form-item label="创建人id" prop="creator">
        <el-input v-model="dataForm.creator" placeholder="创建人id"></el-input>
      </el-form-item>
      <el-form-item label="创建人" prop="createBy">
        <el-input v-model="dataForm.createBy" placeholder="创建人"></el-input>
      </el-form-item>
      <el-form-item label="创建时间" prop="createDate">
        <el-input v-model="dataForm.createDate" placeholder="创建时间"></el-input>
      </el-form-item>
      <el-form-item label="更新人id" prop="updater">
        <el-input v-model="dataForm.updater" placeholder="更新人id"></el-input>
      </el-form-item>
      <el-form-item label="更新人" prop="updateBy">
        <el-input v-model="dataForm.updateBy" placeholder="更新人"></el-input>
      </el-form-item>
      <el-form-item label="更新时间" prop="updateDate">
        <el-input v-model="dataForm.updateDate" placeholder="更新时间"></el-input>
      </el-form-item>
    </el-form>
    <template v-slot:footer>
      <el-button @click="visible = false">{{ $t("cancel") }}</el-button>
      <el-button type="primary" @click="dataFormSubmitHandle()">{{ $t("confirm") }}</el-button>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { reactive, ref } from "vue";
import baseService from "@/service/baseService";
import { useI18n } from "vue-i18n";
import { ElMessage } from "element-plus";
const { t } = useI18n();
const emit = defineEmits(["refreshDataList"]);

const visible = ref(false);
const dataFormRef = ref();

const dataForm = reactive({
  id: "",
  bookingCfgId: "",
  carUserId: "",
  carUserName: "",
  carUserPhone: "",
  userStatus: "",
  deptId: "",
  companyUserId: "",
  creator: "",
  createBy: "",
  createDate: "",
  updater: "",
  updateBy: "",
  updateDate: "",
});

const rules = ref({
});

const init = (id?: number) => {
  visible.value = true;
  dataForm.id = "";

  // 重置表单数据
  if (dataFormRef.value) {
    dataFormRef.value.resetFields();
  }

  if (id) {
    getInfo(id);
  }
};

// 获取信息
const getInfo = (id: number) => {
  baseService.get("/dj/bookinguserrecord/" + id).then((res) => {
    Object.assign(dataForm, res.data);
  });
};

// 表单提交
const dataFormSubmitHandle = () => {
  dataFormRef.value.validate((valid: boolean) => {
    if (!valid) {
      return false;
    }
    (!dataForm.id ? baseService.post : baseService.put)("/dj/bookinguserrecord", dataForm).then((res) => {
      ElMessage.success({
        message: t("prompt.success"),
        duration: 500,
        onClose: () => {
          visible.value = false;
          emit("refreshDataList");
        }
      });
    });
  });
};

defineExpose({
  init
});
</script>