<template>
  <el-dialog v-model="visible" :title="!dataForm.id ? $t('add') : $t('update')" :close-on-click-modal="false" :close-on-press-escape="false">
    <el-form :model="dataForm" :rules="rules" ref="dataFormRef" @keyup.enter="dataFormSubmitHandle()" label-width="120px">
      <el-form-item label="主键" prop="id">
        <el-input v-model="dataForm.id" placeholder="主键"></el-input>
      </el-form-item>
      <el-form-item label="订单id" prop="orderId">
        <el-input v-model="dataForm.orderId" placeholder="订单id"></el-input>
      </el-form-item>
      <el-form-item label="车辆出入表id" prop="carEntryExitId">
        <el-input v-model="dataForm.carEntryExitId" placeholder="车辆出入表id"></el-input>
      </el-form-item>
      <el-form-item label="车牌信息" prop="carId">
        <el-input v-model="dataForm.carId" placeholder="车牌信息"></el-input>
      </el-form-item>
      <el-form-item label="车主姓名" prop="carUserName">
        <el-input v-model="dataForm.carUserName" placeholder="车主姓名"></el-input>
      </el-form-item>
      <el-form-item label="车主电话" prop="carUserPhone">
        <el-input v-model="dataForm.carUserPhone" placeholder="车主电话"></el-input>
      </el-form-item>
      <el-form-item label="车辆类别;1:教练车   2：测试车" prop="carType">
        <el-input v-model="dataForm.carType" placeholder="车辆类别;1:教练车   2：测试车"></el-input>
      </el-form-item>
      <el-form-item label="付款状态;0:待支付 1：已支付" prop="payStatus">
        <el-select v-model="dataForm.payStatus" placeholder="请选择">
          <el-option label="人人" value="0"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="付款账号id;微信openid" prop="payUserOpenId">
        <el-input v-model="dataForm.payUserOpenId" placeholder="付款账号id;微信openid"></el-input>
      </el-form-item>
      <el-form-item label="驾校名称" prop="schoolName">
        <el-input v-model="dataForm.schoolName" placeholder="驾校名称"></el-input>
      </el-form-item>
    </el-form>
    <template v-slot:footer>
      <el-button @click="visible = false">{{ $t("cancel") }}</el-button>
      <el-button type="primary" @click="dataFormSubmitHandle()">{{ $t("confirm") }}</el-button>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { reactive, ref } from "vue";
import baseService from "@/service/baseService";
import { useI18n } from "vue-i18n";
import { ElMessage } from "element-plus";
const { t } = useI18n();
const emit = defineEmits(["refreshDataList"]);

const visible = ref(false);
const dataFormRef = ref();

const dataForm = reactive({
  id: "",
  orderId: "",
  carEntryExitId: "",
  carId: "",
  carUserName: "",
  carUserPhone: "",
  carType: "",
  payStatus: "",
  payUserOpenId: "",
  deptId: "",
  schoolName: "",
  schoolUserId: "",
  creator: "",
  createBy: "",
  createDate: "",
  updater: "",
  updateBy: "",
  updateDate: "",
});

const rules = ref({
});

const init = (id?: number) => {
  visible.value = true;
  dataForm.id = "";

  // 重置表单数据
  if (dataFormRef.value) {
    dataFormRef.value.resetFields();
  }

  if (id) {
    getInfo(id);
  }
};

// 获取信息
const getInfo = (id: number) => {
  baseService.get("/dj/carorderinfo/" + id).then((res) => {
    Object.assign(dataForm, res.data);
  });
};

// 表单提交
const dataFormSubmitHandle = () => {
  dataFormRef.value.validate((valid: boolean) => {
    if (!valid) {
      return false;
    }
    (!dataForm.id ? baseService.post : baseService.put)("/dj/carorderinfo", dataForm).then((res) => {
      ElMessage.success({
        message: t("prompt.success"),
        duration: 500,
        onClose: () => {
          visible.value = false;
          emit("refreshDataList");
        }
      });
    });
  });
};

defineExpose({
  init
});
</script>
