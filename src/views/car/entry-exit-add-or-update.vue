<template>
  <el-dialog v-model="visible" :title="!dataForm.id ? $t('add') : $t('update')" :close-on-click-modal="false" :close-on-press-escape="false">
    <el-form :model="dataForm" :rules="rules" ref="dataFormRef" @keyup.enter="dataFormSubmitHandle()" label-width="120px">
      <el-form-item label="主键" prop="id">
        <el-input v-model="dataForm.id" placeholder="主键"></el-input>
      </el-form-item>
      <el-form-item label="车牌信息" prop="carId">
        <el-input v-model="dataForm.carId" placeholder="车牌信息"></el-input>
      </el-form-item>
      <el-form-item label="入库时间" prop="warehousingDate">
        <el-input v-model="dataForm.warehousingDate" placeholder="入库时间"></el-input>
      </el-form-item>
      <el-form-item label="出库时间" prop="outboundDate">
        <el-input v-model="dataForm.outboundDate" placeholder="出库时间"></el-input>
      </el-form-item>
      <el-form-item label="公司id" prop="deptId">
        <el-input v-model="dataForm.deptId" placeholder="驾校id"></el-input>
      </el-form-item>
      <el-form-item label="驾校名称" prop="schoolName">
        <el-input v-model="dataForm.schoolName" placeholder="驾校名称"></el-input>
      </el-form-item>
      <el-form-item label="公司用户id" prop="schoolUserId">
        <el-input v-model="dataForm.schoolUserId" placeholder="驾校用户id"></el-input>
      </el-form-item>
      <el-form-item label="白名单过期时间;默认为null，即为没有" prop="whitelistExpirationDate">
        <el-input v-model="dataForm.whitelistExpirationDate" placeholder="白名单过期时间;默认为null，即为没有"></el-input>
      </el-form-item>
      <el-form-item label="车牌信息录入id" prop="carInputInfoId">
        <el-input v-model="dataForm.carInputInfoId" placeholder="车牌信息录入id"></el-input>
      </el-form-item>
    </el-form>
    <template v-slot:footer>
      <el-button @click="visible = false">{{ $t("cancel") }}</el-button>
      <el-button type="primary" @click="dataFormSubmitHandle()">{{ $t("confirm") }}</el-button>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { reactive, ref } from "vue";
import baseService from "@/service/baseService";
import { useI18n } from "vue-i18n";
import { ElMessage } from "element-plus";
const { t } = useI18n();
const emit = defineEmits(["refreshDataList"]);

const visible = ref(false);
const dataFormRef = ref();

const dataForm = reactive({
  id: "",
  carId: "",
  warehousingDate: "",
  outboundDate: "",
  deptId: "",
  schoolName: "",
  schoolUserId: "",
  whitelistExpirationDate: "",
  carInputInfoId: "",
});

const rules = ref({
});

const init = (id?: number) => {
  visible.value = true;
  dataForm.id = "";

  // 重置表单数据
  if (dataFormRef.value) {
    dataFormRef.value.resetFields();
  }

  if (id) {
    getInfo(id);
  }
};

// 获取信息
const getInfo = (id: number) => {
  baseService.get("/car/entryExit/" + id).then((res) => {
    Object.assign(dataForm, res.data);
  });
};

// 表单提交
const dataFormSubmitHandle = () => {
  dataFormRef.value.validate((valid: boolean) => {
    if (!valid) {
      return false;
    }
    (!dataForm.id ? baseService.post : baseService.put)("/car/entryExit", dataForm).then((res) => {
      ElMessage.success({
        message: t("prompt.success"),
        duration: 500,
        onClose: () => {
          visible.value = false;
          emit("refreshDataList");
        }
      });
    });
  });
};

defineExpose({
  init
});
</script>
