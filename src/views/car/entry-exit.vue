<template>
  <div class="mod-dj__carentryexitinfo">
    <el-form :inline="true" :model="state.dataForm" @keyup.enter="state.getDataList()">
      <el-form-item>
        <el-select v-model="state.dataForm.carType" placeholder="车辆类别">
          <el-option
            v-for="(value, key) in carTypes"
            :key="key"
            :label="value"
            :value="key"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-input v-model="state.dataForm.carId" placeholder="车牌信息" clearable></el-input>
      </el-form-item>
<!--      <el-form-item>-->
<!--        <el-input v-model="state.dataForm.schoolName" placeholder="考场名称" clearable></el-input>-->
<!--      </el-form-item>-->
      <el-form-item>
        <el-form-item label="">
          <el-date-picker
            v-model="state.warehousingDate"
            type="datetimerange"
            format="YYYY-MM-DD HH:mm:ss"
            value-format="YYYY-MM-DD HH:mm:ss"
            :default-time="new Date()"
            start-placeholder="开始时间"
            end-placeholder="结束时间"></el-date-picker>
        </el-form-item>
        <el-button @click="handleSearch">{{ $t("query") }}</el-button>
      </el-form-item>
      <el-form-item>
        <el-button type="info" @click="state.exportHandle()">{{ $t("export") }}</el-button>
      </el-form-item>
      <el-form-item>
        <el-button v-if="state.hasPermission('dj:carentryexitinfo:save')" type="primary" @click="addOrUpdateHandle()">{{ $t("add") }}</el-button>
      </el-form-item>
      <el-form-item>
        <el-button v-if="state.hasPermission('dj:carentryexitinfo:delete')" type="danger" @click="state.deleteHandle()">{{ $t("deleteBatch") }}</el-button>
      </el-form-item>
    </el-form>
    <el-table v-loading="state.dataListLoading" :data="state.dataList" border @selection-change="state.dataListSelectionChangeHandle" style="width: 100%">
      <el-table-column type="selection" header-align="center" align="center" width="50"></el-table-column>
      <el-table-column prop="carType" label="车辆类别" header-align="center" align="center" :formatter="formatCarType"></el-table-column>
      <el-table-column prop="carId" label="车牌信息" header-align="center" align="center"></el-table-column>
      <el-table-column prop="carUserName" label="车主姓名" header-align="center" align="center"></el-table-column>
      <el-table-column prop="carUserPhone" label="车主电话" header-align="center" align="center"></el-table-column>
      <el-table-column prop="warehousingDate" label="入库时间" header-align="center" align="center"></el-table-column>
      <el-table-column prop="outboundDate" label="出库时间" header-align="center" align="center"></el-table-column>
      <el-table-column prop="schoolName" label="驾校名称" header-align="center" align="center"></el-table-column>
<!--      <el-table-column prop="examinationRoomName" label="考场名称" header-align="center" align="center"></el-table-column>-->
      <el-table-column prop="createDate" label="创建时间" header-align="center" align="center"></el-table-column>
<!--      <el-table-column prop="updateDate" label="更新时间" header-align="center" align="center"></el-table-column>-->
      <el-table-column :label="$t('handle')" fixed="right" header-align="center" align="center" width="150">
        <template v-slot="scope">
          <el-button v-if="state.hasPermission('dj:carentryexitinfo:update')" type="primary" link @click="addOrUpdateHandle(scope.row.id)">{{ $t("update") }}</el-button>
          <el-button v-if="state.hasPermission('dj:carentryexitinfo:delete')" type="primary" link @click="state.deleteHandle(scope.row.id)">{{ $t("delete") }}</el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination :current-page="state.page" :page-sizes="[10, 20, 50, 100]" :page-size="state.limit" :total="state.total" layout="total, sizes, prev, pager, next, jumper" @size-change="state.pageSizeChangeHandle" @current-change="state.pageCurrentChangeHandle"> </el-pagination>
    <!-- 弹窗, 新增 / 修改 -->
    <add-or-update :key="addKey" ref="addOrUpdateRef" @refreshDataList="state.getDataList"></add-or-update>
  </div>
</template>

<script lang="ts" setup>
import useView from "@/hooks/useView";
import { nextTick, reactive, ref, toRefs, watch } from "vue";
import AddOrUpdate from "./entry-exit-add-or-update.vue";

const view = reactive({
  getDataListURL: "/car/entryExit/page",
  getDataListIsPage: true,
  exportURL: "/car/entryExit/export",
  deleteURL: "/car/entryExit",
  deleteIsBatch: true,
  dataForm: {
    carType: "",
    carId: "",
    warehousingDate: "",
    outboundDate: "",
    schoolName: "",
    startTime:'',
    endTime:''
  },
  warehousingDate:''
});

const state = reactive({ ...useView(view), ...toRefs(view) });

// 定义车辆类别映射
const carTypes = {
  1: '教练车',
  2: '测试车',
  3: '免费车辆',
  // 添加其他支付方式...
};
// 添加格式化方法
const formatCarType = (row, column, cellValue) => {
  return carTypes[cellValue] || '未知';
};

const addKey = ref(0);
const addOrUpdateRef = ref();
const addOrUpdateHandle = (id?: number) => {
  addKey.value++;
  nextTick(() => {
    addOrUpdateRef.value.init(id);
  });
};
const handleSearch = () => {
  if(state.warehousingDate){
    state.dataForm.startTime = state.warehousingDate[0];
    state.dataForm.endTime = state.warehousingDate[1];
  } else {
    state.dataForm.startTime = '';
    state.dataForm.endTime = '';
  }
  state.getDataList();
};

</script>
