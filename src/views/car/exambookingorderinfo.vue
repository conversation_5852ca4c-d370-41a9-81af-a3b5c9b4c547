<template>
  <div class="mod-dj__exambookingorderinfo">
    <el-form :inline="true" :model="state.dataForm" @keyup.enter="state.getDataList()">
      <el-form-item>
        <el-input v-model="state.dataForm.studentName" placeholder="学员姓名" clearable></el-input>
      </el-form-item>
      <el-form-item>
        <el-input v-model="state.dataForm.studentIdCard" placeholder="学员身份证" clearable></el-input>
      </el-form-item>
      <el-form-item>
        <el-input v-model="state.dataForm.coachName" placeholder="教练姓名" clearable></el-input>
      </el-form-item>
      <el-form-item>
        <el-input v-model="state.dataForm.coachPhone" placeholder="教练电话" clearable></el-input>
      </el-form-item>
      <el-form-item>
        <el-input v-model="state.dataForm.schoolName" placeholder="驾校名称" clearable></el-input>
      </el-form-item>
      <el-form-item label="">
        <el-date-picker
          v-model="state.warehousingDate"
          type="datetimerange"
          format="YYYY-MM-DD HH:mm:ss"
          value-format="YYYY-MM-DD HH:mm:ss"
          :default-time="new Date()"
          start-placeholder="开始时间"
          end-placeholder="结束时间"></el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button @click="handleSearch">{{ $t("query") }}</el-button>
      </el-form-item>
      <el-form-item>
        <el-button type="info" @click="state.exportHandle()">{{ $t("export") }}</el-button>
      </el-form-item>
      <el-form-item>
        <el-button v-if="state.hasPermission('dj:exambookingorderinfo:save')" type="primary" @click="addOrUpdateHandle()">{{ $t("add") }}</el-button>
      </el-form-item>
      <el-form-item>
        <el-button v-if="state.hasPermission('dj:exambookingorderinfo:delete')" type="danger" @click="state.deleteHandle()">{{ $t("deleteBatch") }}</el-button>
      </el-form-item>
    </el-form>
    <el-table v-loading="state.dataListLoading" :data="state.dataList" border @selection-change="state.dataListSelectionChangeHandle" style="width: 100%">
      <el-table-column type="selection" header-align="center" align="center" width="50"></el-table-column>
      <el-table-column prop="carUserName" label="教练姓名" header-align="center" align="center"></el-table-column>
      <el-table-column prop="carUserPhone" label="教练账号" header-align="center" align="center"></el-table-column>
      <el-table-column prop="studentName" label="学员姓名" header-align="center" align="center"></el-table-column>
      <el-table-column prop="studentIdCard" label="学员身份证" header-align="center" align="center"></el-table-column>
      <el-table-column prop="studentPhone" label="学员手机" header-align="center" align="center"></el-table-column>
      <el-table-column prop="schoolName" label="驾校" header-align="center" align="center"></el-table-column>
      <el-table-column prop="bookingTime" label="预约日期" header-align="center" align="center"></el-table-column>
      <el-table-column prop="orderMoney" label="收费金额" header-align="center" align="center"></el-table-column>
      <el-table-column prop="updateDate" label="支付时间" header-align="center" align="center"></el-table-column>
      <el-table-column prop="shareMoney" label="教练返利" header-align="center" align="center"></el-table-column>
      <el-table-column prop="carType" label="预约车型" header-align="center" align="center">
        <template v-slot="scope">
          <span>{{ scope.row.carType==1?'C1':'C2' }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('handle')" fixed="right" header-align="center" align="center" width="150">
        <template v-slot="scope">
          <el-button v-if="state.hasPermission('dj:exambookingorderinfo:update')" type="primary" link @click="addOrUpdateHandle(scope.row.id)">{{ $t("update") }}</el-button>
          <el-button v-if="state.hasPermission('dj:exambookingorderinfo:delete')" type="primary" link @click="state.deleteHandle(scope.row.id)">{{ $t("delete") }}</el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination :current-page="state.page" :page-sizes="[10, 20, 50, 100]" :page-size="state.limit" :total="state.total" layout="total, sizes, prev, pager, next, jumper" @size-change="state.pageSizeChangeHandle" @current-change="state.pageCurrentChangeHandle"> </el-pagination>
    <!-- 弹窗, 新增 / 修改 -->
    <add-or-update :key="addKey" ref="addOrUpdateRef" @refreshDataList="state.getDataList"></add-or-update>
  </div>
</template>

<script lang="ts" setup>
import useView from "@/hooks/useView";
import { nextTick, reactive, ref, toRefs, watch } from "vue";
import AddOrUpdate from "./exambookingorderinfo-add-or-update.vue";

const view = reactive({
  getDataListURL: "/dj/exambookingorderinfo/page",
  getDataListIsPage: true,
  exportURL: "/dj/exambookingorderinfo/export",
  deleteURL: "/dj/exambookingorderinfo",
  deleteIsBatch: true,
  dataForm: {
    studentIdCard: "",
    studentName: "",
    schoolName: "",
    coachName: "",
    coachPhone:'',
    startTime:'',
    endTime:''
  },
  warehousingDate:''
});

const state = reactive({ ...useView(view), ...toRefs(view) });


const addKey = ref(0);
const addOrUpdateRef = ref();
const addOrUpdateHandle = (id?: number) => {
  addKey.value++;
  nextTick(() => {
    addOrUpdateRef.value.init(id);
  });
};

const handleSearch = () => {
  if(state.warehousingDate){
    state.dataForm.startTime = state.warehousingDate[0];
    state.dataForm.endTime = state.warehousingDate[1];
  } else {
    state.dataForm.startTime = '';
    state.dataForm.endTime = '';
  }
  state.getDataList();
};
</script>
