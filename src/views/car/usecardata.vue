<template>
  <div class="mod-dj__userrebateinfo">
    <el-form :inline="true" :model="state.dataForm" @keyup.enter="state.getDataList()">
      <el-form-item>
        <el-input v-model="state.dataForm.carId" placeholder="车牌信息" clearable></el-input>
      </el-form-item>
      <el-form-item>
        <el-input v-model="state.dataForm.carUserName" placeholder="车辆人姓名" clearable></el-input>
      </el-form-item>
      <el-form-item>
        <el-input v-model="state.dataForm.carUserPhone" placeholder="车辆人电话" clearable></el-input>
      </el-form-item>
      <el-form-item>
        <el-select v-model="state.dataForm.carType" placeholder="车辆类别">
          <el-option
            v-for="(value, key) in carTypes"
            :key="key"
            :label="value"
            :value="key"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-input v-model="state.dataForm.schoolName" placeholder="所属驾校名称" clearable></el-input>
      </el-form-item>
      <el-form-item label="">
        <el-date-picker
          v-model="state.warehousingDate"
          type="datetimerange"
          format="YYYY-MM-DD HH:mm:ss"
          value-format="YYYY-MM-DD HH:mm:ss"
          :default-time="new Date()"
          start-placeholder="开始时间"
          end-placeholder="结束时间"></el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button @click="handleSearch()">{{ $t("query") }}</el-button>
      </el-form-item>
      <el-form-item>
        <el-button type="info" @click="state.exportHandle()">{{ $t("export") }}</el-button>
      </el-form-item>
      <el-form-item>
        <el-button v-if="state.hasPermission('dj:userrebateinfo:save')" type="primary" @click="addOrUpdateHandle()">{{ $t("add") }}</el-button>
      </el-form-item>
      <el-form-item>
        <el-button v-if="state.hasPermission('dj:userrebateinfo:delete')" type="danger" @click="state.deleteHandle()">{{ $t("deleteBatch") }}</el-button>
      </el-form-item>
    </el-form>
    <table class="dtl-table">
      <tbody>
        <tr>
          <td class="dtl-head">累计消费金额</td>
          <td>{{ countInfo.allOrderMoney == null ? 0 : countInfo.allOrderMoney}}元</td>
          <td class="dtl-head">累计教练金额</td>
          <td>{{ countInfo.allShareMoney == null ? 0 : countInfo.allShareMoney}}元</td>
          <td class="dtl-head">累计考场金额</td>
          <td>{{ countInfo.allExaminationRoomMoney == null ? 0 : countInfo.allExaminationRoomMoney}}元</td>
        </tr>
      </tbody>
    </table>
    <el-table v-loading="state.dataListLoading" :data="state.dataList" border style="width: 100%; margin-top: 30px" @selection-change="state.dataListSelectionChangeHandle">
      <el-table-column type="selection" header-align="center" align="center" width="50"></el-table-column>
      <el-table-column prop="carId" label="车牌信息" header-align="center" align="center"></el-table-column>
      <el-table-column prop="carUserName" label="车主姓名" header-align="center" align="center"></el-table-column>
      <el-table-column prop="carUserPhone" label="车主电话" header-align="center" align="center"></el-table-column>
      <el-table-column prop="carType" label="车辆类别" header-align="center" align="center" :formatter="formatCarType"></el-table-column>
      <el-table-column prop="schoolName" label="驾校名称" header-align="center" align="center"></el-table-column>
      <el-table-column prop="trainerName" label="带训人" header-align="center" align="center"></el-table-column>
<!--      <el-table-column prop="examinationRoomName" label="考场名称" header-align="center" align="center"></el-table-column>-->
      <el-table-column prop="warehousingDate" label="入场时间" header-align="center" align="center"></el-table-column>
      <el-table-column prop="outboundDate" label="出场时间" header-align="center" align="center"></el-table-column>
      <el-table-column prop="trainingDuration" label="练车时长" header-align="center" align="center"></el-table-column>
      <el-table-column label="消费金额" header-align="center" align="center">
        <template v-slot="scope">
          <span>{{ scope.row.orderMoney }}元</span>
        </template>
      </el-table-column>
<!--      <el-table-column label="教练金额" header-align="center" align="center">-->
<!--        <template v-slot="scope">-->
<!--          <span>{{ scope.row.shareMoney }}元</span>-->
<!--        </template>-->
<!--      </el-table-column>-->
<!--      <el-table-column label="考场金额" header-align="center" align="center">-->
<!--        <template v-slot="scope">-->
<!--          <span>{{ scope.row.examinationRoomMoney }}元</span>-->
<!--        </template>-->
<!--      </el-table-column>-->
      <el-table-column :label="$t('handle')" fixed="right" header-align="center" align="center" width="150">
        <template v-slot="scope">
          <el-button v-if="state.hasPermission('dj:userrebateinfo:update')" type="primary" link @click="addOrUpdateHandle(scope.row.id)">{{ $t("update") }}</el-button>
          <el-button v-if="state.hasPermission('dj:userrebateinfo:delete')" type="primary" link @click="state.deleteHandle(scope.row.id)">{{ $t("delete") }}</el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination :current-page="state.page" :page-sizes="[10, 20, 50, 100]" :page-size="state.limit" :total="state.total" layout="total, sizes, prev, pager, next, jumper" @size-change="state.pageSizeChangeHandle" @current-change="state.pageCurrentChangeHandle"> </el-pagination>
    <!-- 弹窗, 新增 / 修改 -->
    <add-or-update :key="addKey" ref="addOrUpdateRef" @refreshDataList="state.getDataList"></add-or-update>
  </div>
</template>

<script lang="ts" setup>
import useView from "@/hooks/useView";
import { handleError, nextTick, onMounted, reactive, ref, toRefs, watch } from "vue";
import AddOrUpdate from "./userrebateinfo-add-or-update.vue";
import baseService from "@/service/baseService";
import { ElMessage } from "element-plus";
import { useI18n } from "vue-i18n";

const { t } = useI18n();
const view = reactive({
  getDataListURL: "/car/useData/userDataPage",
  getDataListIsPage: true,
  exportURL: "/car/useData/export",
  deleteIsBatch: true,
  dataForm: {
    carId: "",
    carUserName: "",
    carUserPhone: "",
    carType: "",
    schoolName: "",
    whetherRebate: "",
    orderId: "",
    startTime:'',
    endTime:''
  },
  countInfo:{
    allOrderMoney:0,
    allShareMoney:0,
    allExaminationRoomMoney:0
  },
  warehousingDate:''
});

const state = reactive({ ...useView(view), ...toRefs(view) });

onMounted(()=>{
  getAllSpeedDataInfo();
});

const getAllSpeedDataInfo = async () =>{
  try {
    const response = await baseService.get( `/car/useData/getAllSpeedDataInfo?carId=${state.dataForm.carId}&carUserName=${state.dataForm.carUserName}&carUserPhone=${state.dataForm.carUserPhone}&carType=${state.dataForm.carType}&schoolName=${state.dataForm.schoolName}&whetherRebate=${state.dataForm.whetherRebate}&orderId=${state.dataForm.orderId}&startTime=${state.dataForm.startTime}&endTime=${state.dataForm.endTime}`); // 假设这是获取驾校列表的接口
    if(response.data) countInfo.value = response.data;
  } catch (error) {
    ElMessage.error({ message: t("获取数据错误"), duration: 3000 });
  }
};

// 定义车辆类别映射
const carTypes = {
  1: '教练车',
  2: '测试车',
  3: '带训车',
  99: '模拟预约考试'
  // 添加其他支付方式...
};
// 添加格式化方法
const formatCarType = (row, column, cellValue) => {
  return carTypes[cellValue] || '未知';
};

//定义是否返利映射
const whetherRebates = {
  1: '已返利',
  0: '没返利',
  // 添加其他支付方式...
};
// 添加格式化方法
const formatWhetherRebate = (row, column, cellValue) => {
  return whetherRebates[cellValue] || '未知';
};

const countInfo = ref<any>({});
const addKey = ref(0);
const addOrUpdateRef = ref();
const addOrUpdateHandle = (id?: number) => {
  addKey.value++;
  nextTick(() => {
    addOrUpdateRef.value.init(id);
  });
};

// const handleSearch = () => {
//
// }

const handleSearch = () => {
  if(state.warehousingDate){
    state.dataForm.startTime = state.warehousingDate[0];
    state.dataForm.endTime = state.warehousingDate[1];
  } else {
    state.dataForm.startTime = '';
    state.dataForm.endTime = '';
  }
  state.getDataList();
  getAllSpeedDataInfo()
};
</script>
<style lang="less" scoped>
.dtl-table {
  width: 100%;
  table-layout: fixed;
  border-collapse: collapse;
  margin-top: 1em;
}

.dtl-table td {
  text-align: center;
  padding: 0.3em;
  border: 1px solid #dfe6ec;
}

.dtl-head {
  background: #eef1f6;
}
</style>
