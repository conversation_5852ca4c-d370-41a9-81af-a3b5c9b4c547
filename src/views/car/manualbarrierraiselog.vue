<template>
  <div class="mod-dj__manualbarrierraiselog">
    <el-form :inline="true" :model="state.dataForm" @keyup.enter="state.getDataList()">
<!--      <el-form-item>-->
<!--        <el-input v-model="state.dataForm.examinationRoomName" placeholder="考场名称" clearable></el-input>-->
<!--      </el-form-item>-->
      <el-form-item>
        <el-input v-model="state.dataForm.carId" placeholder="车牌信息" clearable></el-input>
      </el-form-item>
      <el-form-item label="">
        <el-date-picker
          v-model="state.createDate"
          type="datetimerange"
          format="YYYY-MM-DD HH:mm:ss"
          value-format="YYYY-MM-DD HH:mm:ss"
          :default-time="new Date()"
          start-placeholder="开始时间"
          end-placeholder="结束时间"></el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button @click="handleSearch">{{ $t("query") }}</el-button>
      </el-form-item>
      <el-form-item>
        <el-button type="info" @click="state.exportHandle()">{{ $t("export") }}</el-button>
      </el-form-item>
      <el-form-item>
        <el-button v-if="state.hasPermission('dj:manualbarrierraiselog:save')" type="primary" @click="addOrUpdateHandle()">{{ $t("add") }}</el-button>
      </el-form-item>

      <el-form-item>
        <el-button v-if="state.hasPermission('dj:manualbarrierraiselog:openTheGate')" type="primary" @click="handleOpZaDao(1)">入场抬杆</el-button>
      </el-form-item>
      <el-form-item>
        <el-button v-if="state.hasPermission('dj:manualbarrierraiselog:openTheGate')" type="primary" @click="handleOpZaDao(0)">出场抬杆</el-button>
      </el-form-item>
      <el-form-item>
        <el-button v-if="state.hasPermission('dj:manualbarrierraiselog:delete')" type="danger" @click="state.deleteHandle()">{{ $t("deleteBatch") }}</el-button>
      </el-form-item>
    </el-form>
    <el-table v-loading="state.dataListLoading" :data="state.dataList" border @selection-change="state.dataListSelectionChangeHandle" style="width: 100%">
      <el-table-column type="selection" header-align="center" align="center" width="50"></el-table-column>
      <el-table-column prop="carId" label="车牌" header-align="center" align="center"></el-table-column>
      <el-table-column prop="manualBarrierRaiseType" label="抬杆类型" header-align="center" align="center"></el-table-column>
      <el-table-column prop="createBy" label="操作人员" header-align="center" align="center"></el-table-column>
      <el-table-column prop="createDate" label="抬杆时间" header-align="center" align="center"></el-table-column>
      <el-table-column :label="$t('handle')" fixed="right" header-align="center" align="center" width="150">
        <template v-slot="scope">
          <el-button v-if="state.hasPermission('dj:manualbarrierraiselog:update')" type="primary" link @click="addOrUpdateHandle(scope.row.id)">{{ $t("update") }}</el-button>
          <el-button v-if="state.hasPermission('dj:manualbarrierraiselog:delete')" type="primary" link @click="state.deleteHandle(scope.row.id)">{{ $t("delete") }}</el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination :current-page="state.page" :page-sizes="[10, 20, 50, 100]" :page-size="state.limit" :total="state.total" layout="total, sizes, prev, pager, next, jumper" @size-change="state.pageSizeChangeHandle" @current-change="state.pageCurrentChangeHandle"> </el-pagination>
    <!-- 弹窗, 新增 / 修改 -->
    <add-or-update :key="addKey" ref="addOrUpdateRef" @refreshDataList="state.getDataList"></add-or-update>
  </div>
</template>

<script lang="ts" setup>
import useView from "@/hooks/useView";
import { nextTick, reactive, ref, toRefs, watch } from "vue";
import AddOrUpdate from "./manualbarrierraiselog-add-or-update.vue";
import baseService from "@/service/baseService";
import { ElMessage } from "element-plus";

const view = reactive({
  getDataListURL: "/dj/manualbarrierraiselog/page",
  getDataListIsPage: true,
  exportURL: "/dj/manualbarrierraiselog/export",
  deleteURL: "/dj/manualbarrierraiselog",
  deleteIsBatch: true,
  dataForm: {
    createDate: "",
    updateDate:"",
    carId:""
  },
  createDate:''
});

const state = reactive({ ...useView(view), ...toRefs(view) });


const addKey = ref(0);
const addOrUpdateRef = ref();
const addOrUpdateHandle = (id?: number) => {
  addKey.value++;
  nextTick(() => {
    addOrUpdateRef.value.init(id);
  });
};

const handleSearch = () => {
  if(state.createDate){
    state.dataForm.createDate = state.createDate[0];
    state.dataForm.updateDate = state.createDate[1];
  } else {
    state.dataForm.createDate = '';
    state.dataForm.updateDate = '';
  }
  state.getDataList();
};

const handleOpZaDao = (type:any) => {
  baseService
    .get('/dj/manualbarrierraiselog/openTheGate', {
      manualBarrierRaise: type
    })
    .then((res) => {
      if(res.code==0){
        state.getDataList();
      } else {
        ElMessage.warning({
          message: res.msg,
          duration: 500
        });
      }

    })
    .catch(() => {
      state.dataListLoading = false;
    });
}
</script>
