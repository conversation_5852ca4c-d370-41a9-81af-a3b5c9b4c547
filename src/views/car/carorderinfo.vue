<template>
  <div class="mod-dj__carorderinfo">
    <el-form :inline="true" :model="state.dataForm" @keyup.enter="state.getDataList()">
      <el-form-item>
        <el-input v-model="state.dataForm.carId" placeholder="车牌信息" clearable></el-input>
      </el-form-item>
      <el-form-item>
        <el-input v-model="state.dataForm.carUserName" placeholder="车主姓名" clearable></el-input>
      </el-form-item>
      <el-form-item>
        <el-input v-model="state.dataForm.carUserPhone" placeholder="车主电话" clearable></el-input>
      </el-form-item>
      <el-form-item>
        <el-input v-model="state.dataForm.schoolName" placeholder="驾校名称" clearable></el-input>
      </el-form-item>
      <el-form-item>
        <el-select v-model="state.dataForm.carType" placeholder="请选择车辆类别" clearable>
          <el-option
            v-for="(value, key) in carTypes"
            :key="key"
            :label="value"
            :value="key"
            :selected-label="value"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="">
        <el-date-picker
          v-model="state.warehousingDate"
          type="datetimerange"
          format="YYYY-MM-DD HH:mm:ss"
          value-format="YYYY-MM-DD HH:mm:ss"
          :default-time="new Date()"
          start-placeholder="开始时间"
          end-placeholder="结束时间"></el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button @click="handleSearch">{{ $t("query") }}</el-button>
      </el-form-item>
      <el-form-item>
        <el-button type="info" @click="state.exportHandle()">{{ $t("export") }}</el-button>
      </el-form-item>
      <el-form-item>
        <el-button v-if="state.hasPermission('dj:carorderinfo:save')" type="primary" @click="addOrUpdateHandle()">{{ $t("add") }}</el-button>
      </el-form-item>
      <el-form-item>
        <el-button v-if="state.hasPermission('dj:carorderinfo:delete')" type="danger" @click="state.deleteHandle()">{{ $t("deleteBatch") }}</el-button>
      </el-form-item>
    </el-form>
    <el-table v-loading="state.dataListLoading" :data="state.dataList" border @selection-change="state.dataListSelectionChangeHandle" style="width: 100%">
      <el-table-column type="selection" header-align="center" align="center" width="50"></el-table-column>
      <el-table-column prop="orderId" label="订单id" show-overflow-tooltip header-align="center" width="150" align="center"></el-table-column>
      <el-table-column prop="payUserOpenId" label="付款账号" show-overflow-tooltip header-align="center" width="150" align="center"></el-table-column>
      <el-table-column prop="carId" label="车牌信息" header-align="center" width="100" align="center"></el-table-column>
      <el-table-column prop="carUserName" label="车主姓名" header-align="center" width="100" align="center"></el-table-column>
      <el-table-column prop="carUserPhone" label="车主电话" header-align="center" width="120" align="center"></el-table-column>
      <el-table-column prop="coachIdCard" label="车主身份证" header-align="center" width="180" align="center"></el-table-column>
      <el-table-column prop="carType" label="车辆类别" header-align="center" width="100" align="center" :formatter="formatCarType"></el-table-column>
      <el-table-column prop="trainerName" label="带训人" header-align="center" width="100" align="center"></el-table-column>
      <el-table-column prop="carType" label="订单状态" header-align="center" width="100" align="center">
        <template v-slot="scope">
          <span :class="getStatusClass(scope.row.payStatus)">{{ getStatusText(scope.row.payStatus) }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="schoolName" label="驾校名称" header-align="center" width="120" align="center"></el-table-column>
      <el-table-column prop="warehousingDate" label="入场时间" header-align="center" width="160" align="center"></el-table-column>
      <el-table-column prop="outboundDate" label="出场时间" header-align="center" width="160" align="center"></el-table-column>
      <el-table-column prop="trainingDuration" label="练车时长" header-align="center" width="150" align="center"></el-table-column>
      <el-table-column prop="startWhitelistDate" label="免费开始时间" header-align="center" width="160" align="center">
        <template v-slot="scope">
          <span>{{ scope.row.startWhitelistDate }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="endWhitelistDate" label="免费结束时间" header-align="center" width="160" align="center">
        <template v-slot="scope">
          <span>{{ scope.row.endWhitelistDate }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="freeTrainingDuration" label="免费练车时长" header-align="center" width="150" align="center">
        <template v-slot="scope">
          <span>{{ scope.row.freeTrainingDuration }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="orderMoney" label="订单金额" header-align="center" width="150" align="center">
        <template v-slot="scope">
          <span>{{ scope.row.orderMoney }}元</span>
        </template>
      </el-table-column>
      <el-table-column prop="shareMoney" label="教练收益" header-align="center" width="150" align="center">
        <template v-slot="scope">
          <span>{{ scope.row.payStatus!=2?(scope.row.shareMoney?scope.row.shareMoney:0)+'元':'--'   }}</span>
        </template>
      </el-table-column>
<!--      <el-table-column prop="ruleConfigParam" label="收费规则" header-align="center" width="300" align="center">-->
<!--        <template v-slot="scope">-->
<!--          <span v-if="scope.row.ruleConfigParam && isJsonString(scope.row.ruleConfigParam)">-->
<!--            {{ formatRuleDescription(scope.row) }}-->
<!--          </span>-->
<!--          <span v-else>无效的规则描述</span>-->
<!--        </template>-->
<!--      </el-table-column>-->
      <el-table-column prop="createDate" label="订单创建时间" header-align="center" width="180" align="center"></el-table-column>
      <el-table-column prop="updateDate" label="订单支付时间" header-align="center" width="180" align="center"></el-table-column>
      <el-table-column :label="$t('handle')" fixed="right" header-align="center" align="center" width="150">
        <template v-slot="scope">
          <el-button v-if="scope.row.payStatus==2 &&state.hasPermission('dj:carorderinfo:update')" type="primary" link @click="recoveryOfTrainingCarOrders(scope.row)">追缴</el-button>
          <el-button v-if="state.hasPermission('dj:carorderinfo:delete')" type="primary" link @click="state.deleteHandle(scope.row.id)">{{ $t("delete") }}</el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination :current-page="state.page" :page-sizes="[10, 20, 50, 100]" :page-size="state.limit" :total="state.total" layout="total, sizes, prev, pager, next, jumper" @size-change="state.pageSizeChangeHandle" @current-change="state.pageCurrentChangeHandle"> </el-pagination>
    <!-- 弹窗, 新增 / 修改 -->
    <add-or-update :key="addKey" ref="addOrUpdateRef" @refreshDataList="state.getDataList"></add-or-update>
  </div>
</template>

<script lang="ts" setup>
import useView from "@/hooks/useView";
import { nextTick, reactive, ref, toRefs, watch } from "vue";
import AddOrUpdate from "./carorderinfo-add-or-update.vue";
import { ElMessage, ElMessageBox } from "element-plus";
import baseService from "@/service/baseService";
const view = reactive({
  getDataListURL: "/dj/carorderinfo/page",
  getDataListIsPage: true,
  exportURL: "/dj/carorderinfo/export",
  deleteURL: "/dj/carorderinfo",
  deleteIsBatch: true,
  dataForm: {
    carId: "",
    carUserName: "",
    carUserPhone: "",
    payStatus: "",
    schoolName: "",
    carType:'',
    startTime:'',
    endTime:''
  },
  warehousingDate:''
});

const state = reactive({ ...useView(view), ...toRefs(view) });


const addKey = ref(0);
const addOrUpdateRef = ref();

// 定义车辆类别映射
const carTypes = {
  1: '教练车',
  2: '测试车',
  3: '带训车',
  // 添加车辆类别...
};
// 添加格式化方法
const formatCarType = (row, column, cellValue) => {
  return carTypes[cellValue] || '未知';
};

// 定义车辆类别映射


const getStatusClass = (status: string | number) => {
  return colorTypes[status];
};


// 新增的方法，检查字符串是否为有效的 JSON
function isJsonString(str) {
  try {
    JSON.parse(str);
    return true;
  } catch (e) {
    return false;
  }
}

// 格式化收费规则描述
function formatRuleDescription(row) {
  const config = JSON.parse(row.ruleConfigParam);
  if (!config || (!config.hourObj && !config.percentObj)) return '无效的规则描述';

  let chargeDesc = '';
  let rebateDesc = '';

  // 添加收费类型描述
  if (config.chargeType === 1) {
    chargeDesc = '每小时 ';
  } else if (config.chargeType === 2) {
    chargeDesc = '每天 ';
  } else {
    chargeDesc = '未知收费周期 ';
  }

  // 添加返利类型描述
  if (config.rebateType === 1) {
    const rebateComPercent = parseInt(config.percentObj.rebateComMoney) / 100;
    const rebateCoachPercent = parseInt(config.percentObj.rebateCoachMoney) / 100;
    rebateDesc = `，公司返利${rebateComPercent * 100}%，教练返利${rebateCoachPercent * 100}%`;
  } else if (config.rebateType === 2) {
    rebateDesc = `，公司返利${config.hourObj.rebateComMoney}元/小时，教练返利${config.hourObj.rebateCoachMoney}元/小时`;
  } else {
    rebateDesc = '，未知返利方式';
  }

  return `${chargeDesc}${config.rebateType === 2?config.hourObj.carSpeedMoney:config.percentObj.carSpeedMoney} 元，不足1小时按1小时收费${rebateDesc ? rebateDesc : ''}`;
}

// 定义订单状态类别映射
const orderTypes = {
  0: '待支付',
  1: '已支付',
  2: '待追缴',
  4: '已追缴',
  // 添加车辆类别...
};

const colorTypes = {
  0: 'orange',
  1: 'green',
  2: 'red',
  4: 'blue',
  // 添加车辆类别...
};
// 添加格式化方法
// 获取状态文本
const getStatusText = (status: string | number) => {
  return orderTypes[status] || '未知';
};

const formData = ref({
  "payStatus":4,
  "payOrderMoney":0,
  "id":null
});
const handleSearch = () => {
  if(state.warehousingDate){
    state.dataForm.startTime = state.warehousingDate[0];
    state.dataForm.endTime = state.warehousingDate[1];
  } else {
    state.dataForm.startTime = '';
    state.dataForm.endTime = '';
  }
  state.getDataList();
};

const recoveryOfTrainingCarOrders = (row?: any) => {
  formData.value.id = row.id;
  ElMessageBox.prompt('追缴金额', '提示', {
    confirmButtonText: '确认',
    cancelButtonText: '取消',
    inputErrorMessage: '请输入追缴金额',
    inputPlaceholder:'订单金额'+(row.orderMoney?row.orderMoney:0)+'元',
    inputValidator: (value) => {
      if (Number(value) > row.orderMoney) {
        return '追缴金额不能大于订单金额';
      }
      return true;
    },
    model: formData,
  })
    .then(({ value }) => {
      formData.value.payOrderMoney = Number(value);
      ElMessageBox.confirm(
        '请再次确认是否已付款',
        '提示',
        {
          confirmButtonText: '确认',
          cancelButtonText: '取消',
          type: 'warning',
        }
      )
        .then(() => {
          baseService
            .post('/admin/carInfo/recoveryOfTrainingCarOrders', formData.value
            )
            .then((res) => {
              if(res.code==0){
                ElMessage.warning({
                  message: '操作成功',
                  duration: 500
                });
                state.getDataList();
              } else {
                ElMessage.warning({
                  message: res.msg,
                  duration: 500
                });
              }

            })
            .catch(() => {
              state.dataListLoading = false;
            });
        })
        .catch(() => {
         console.log('取消===')
        })
    })
    .catch(() => {
      console.log('取消追缴~')
    })
};
</script>
<style>
.red{
  color: red;
}
.green{
  color: green;
}
.orange{
  color: orange;
}
.blue{
  color: blue;
}
</style>
