<template>
  <el-dialog v-model="visible" title="查找车主" :close-on-click-modal="false" :close-on-press-escape="false" width="1000px">
    <div class="mod-dh__customersign">
      <el-form :inline="true" :model="state.dataForm" @keyup.enter="state.getDataList()">
        <el-form-item>
          <el-input v-model="state.dataForm.username" placeholder="车主姓名" clearable></el-input>
        </el-form-item>
        <el-form-item>
          <el-input v-model="state.dataForm.mobile" placeholder="车主电话" clearable></el-input>
        </el-form-item>
        <el-form-item>
          <el-button @click="state.getDataList()">{{ $t("query") }}</el-button>
        </el-form-item>
      </el-form>
      <el-table ref="table" v-loading="state.dataListLoading" highlight-current-row :data="state.dataList" border   @row-click="singleElection"  style="width: 100%">
        <el-table-column prop="username" :label="$t('user.username')" sortable="custom" header-align="center" align="center"></el-table-column>
        <el-table-column prop="deptName" :label="$t('user.schoolName')" header-align="center" align="center"></el-table-column>
        <el-table-column prop="mobile" :label="$t('user.mobile')" sortable="custom" header-align="center" align="center"></el-table-column>
        <el-table-column prop="userType" :label="$t('user.userType')" sortable="custom" header-align="center" align="center">
          <template v-slot="scope">
            <!-- 添加条件判断显示不同文本 -->
            <span v-if="scope.row.userType === 'coach'">{{ "教练" }}</span>
            <span v-else-if="scope.row.userType === 'admin'">{{ "管理员" }}</span>
            <span v-else-if="scope.row.userType === 'superAdmin'">{{ "超级管理员" }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="status" :label="$t('user.status')" sortable="custom" header-align="center" align="center">
          <template v-slot="scope">
            <el-tag v-if="scope.row.status === 0" size="small" type="danger">{{ $t("user.status0") }}</el-tag>
            <el-tag v-else size="small" type="success">{{ $t("user.status1") }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="createDate" :label="$t('user.createDate')" sortable="custom" header-align="center" align="center" width="180"></el-table-column>
        <el-table-column label="操作" width="100" fixed="right">
          <template v-slot="scope">
            <el-radio class="radio" v-model="templateSelection" :label="scope.$index"><el-button type="primary" link>选择</el-button></el-radio>
          </template>
        </el-table-column>
      </el-table>

      <el-pagination :current-page="state.page" :page-sizes="[10, 20, 50, 100]" :page-size="state.limit" :total="state.total" layout="total, sizes, prev, pager, next, jumper" @size-change="state.pageSizeChangeHandle" @current-change="state.pageCurrentChangeHandle"> </el-pagination>
    </div>
    <template v-slot:footer>
      <el-button @click="visible = false">{{ $t("cancel") }}</el-button>
      <el-button type="primary" @click="dataFormSubmitHandle()">{{ $t("confirm") }}</el-button>
    </template>
  </el-dialog>

</template>

<script lang="ts" setup>
import useView from "@/hooks/useView";
import baseService from "@/service/baseService";
import { nextTick, reactive, ref, toRefs, watch,onBeforeMount } from "vue";
import { IObject } from "@/types/interface";
import { useI18n } from "vue-i18n";
import { ElMessage } from "element-plus";
const { t } = useI18n();
const emit = defineEmits(["change"]);

const view = reactive({
  getDataListURL: "/sys/user/page",
  getDataListIsPage: true,
  exportURL: "/dh/customersign/export",
  deleteURL: "/dh/customersign",
  deleteIsBatch: true,
  dataForm: {
    username: "",
    mobile: "",
    loginType: 1
  },
  order: "desc",
  orderField: "create_date",
  dataList:[]
});

const visible = ref(false);
const table = ref();
const state = reactive({ ...useView(view), ...toRefs(view) });
const row = ref<IObject[]>([{}]);
const templateSelection = ref<any>("");
// 定义车辆类别映射
const carTypes = {
  1: '教练车',
  2: '测试车',
  // 添加其他支付方式...
};

// 定义状态类别映射
const carStatus = {
  0: '空闲中',
  1: '使用中',
  9: '已解绑',
};

// 获取状态文本
const getStatusText = (status: string | number) => {
  return carStatus[status] || '未知';
};

// 获取状态对应的CSS类名
const getStatusClass = (status: string | number) => {
  return `status-${status === '0' ? 'idle' : 'in-use'}`;
};

// 添加格式化方法
const formatCarType = (row, column, cellValue) => {
  return carTypes[cellValue] || '未知';
};
//表格行单击选择事件
const singleElection = (data: any) =>  {
  templateSelection.value = state.dataList.findIndex((item:any) => item.id === data.id);
  row.value[0] = data;
};

const tableList = ref<IObject[]>([{}]);
const handleCurrentChange=(data: object)=>{
  row.value[0] = data;
};

onBeforeMount(() => {
  // if(state.hasPermission('dh:customersign:all')){
  //   state.dataForm.dataAuth = 'all'
  // } else {
  //   state.dataForm.dataAuth = 'part'
  // }
});


const dataFormSubmitHandle = () =>{
  if(row.value.length===0){
    return ElMessage.warning("请选择车主");
  }
  visible.value = false;
  emit('change',row.value);
};

const init = ()=>{
  visible.value = true;

  state.dataForm = {
    username: "",
    mobile: "",
    loginType: 1
  };
  state.getDataList();
};

defineExpose({
  init
});

</script>
<style scoped lang="less">
  /deep/.el-table td.el-table__cell div.cell{
    height:auto !important;
  }
</style>
