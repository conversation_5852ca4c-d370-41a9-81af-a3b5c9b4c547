<template>
  <el-dialog v-model="visible" :title="!dataForm.id ? $t('add') : $t('update')" :close-on-click-modal="false" :close-on-press-escape="false">
    <el-form :model="dataForm" :rules="rules" ref="dataFormRef" @keyup.enter="dataFormSubmitHandle()" label-width="120px">
      <!--      <el-form-item label="主键" prop="id">-->
      <!--        <el-input v-model="dataForm.id" placeholder="主键"></el-input>-->
      <!--      </el-form-item>-->
      <!--      <el-form-item label="驾校id" prop="deptId">-->
      <!--        <el-input v-model="dataForm.deptId" placeholder="驾校id"></el-input>-->
      <!--      </el-form-item>-->
      <el-form-item label="支付方式" prop="paymentMethodType">
        <el-select v-model="dataForm.paymentMethodType" placeholder="请选择">
          <el-option
            v-for="(value, key) in paymentMethodTypes"
            :key="key"
            :label="value"
            :value="key"
          ></el-option>
        </el-select>
      </el-form-item>
      <!--      <el-form-item label="考场名称" prop="schoolName">-->
      <!--        <el-select v-model="dataForm.schoolName" placeholder="请选择">-->
      <!--          <el-option label="公共" value="0"></el-option>-->
      <!--        </el-select>-->
      <!--      </el-form-item>-->
<!--      <el-form-item label="考场名称" prop="schoolName">-->
<!--        <el-select v-model="dataForm.deptId" placeholder="请选择考场名称" @change="handleSchoolChange">-->
<!--          <el-option-->
<!--            v-for="item in schoolOptions"-->
<!--            :key="item.id"-->
<!--            :label="item.name"-->
<!--            :value="item.id"-->
<!--            :disabled="disabledSchools.includes(item.id)"-->
<!--          ></el-option>-->
<!--        </el-select>-->
<!--      </el-form-item>-->
      <!--      <el-form-item label="驾校用户id;=0则是公共  !=0则是个性化" prop="schoolUserId">-->
      <!--        <el-input v-model="dataForm.schoolUserId" placeholder="驾校用户id;=0则是公共  !=0则是个性化"></el-input>-->
      <!--      </el-form-item>-->
    </el-form>
    <template v-slot:footer>
      <el-button @click="visible = false">{{ $t("cancel") }}</el-button>
      <el-button type="primary" @click="dataFormSubmitHandle()">{{ $t("confirm") }}</el-button>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { onMounted, PropType, reactive, ref } from "vue";
import baseService from "@/service/baseService";
import { useI18n } from "vue-i18n";
import { ElMessage } from "element-plus";
const { t } = useI18n();
const emit = defineEmits(["refreshDataList"]);

const visible = ref(false);
const dataFormRef = ref();
const schoolOptions = ref([]); // 用于存储驾校选项
const paymentMethodTypes = {
  1: '微信',
  2: '支付宝',
};
const disabledSchools = ref<number[]>([]);

const dataForm = reactive({
  id: "",
  deptId: "",
  paymentMethodType: "1",
  schoolName: "",
  schoolUserId: "",
});

const rules = ref({
  paymentMethodType: [{ required: true, message: t("validate.required"), trigger: "blur" }],
  schoolName: [{ required: true, message: t("validate.required"), trigger: "blur" }],
});

// 接收父组件传递过来的deptIds
const tableDeptIds = defineProps({
  tableDeptIds: {
    type: Array as PropType<number[]>,
    required: true,
  }
});

onMounted(() => {
  fetchSchoolList(); // 页面加载时获取驾校列表
});

// 获取驾校列表
const fetchSchoolList = async () => {
  try {
    const response = await baseService.get('/sys/dept/getSchoolDept'); // 假设这是获取驾校列表的接口
    schoolOptions.value = response.data.map(school => ({ id: school.deptID, name: school.name,userId: school.userId,}));
  } catch (error) {
    ElMessage.error({ message: t("prompt.errorFetchingSchoolList"), duration: 3000 });
  }
};

// 当学校名称改变时，可以在这里处理其他逻辑，比如根据学校ID获取更多信息
const handleSchoolChange = (schoolId: string) => {
  console.log("Selected school ID:", schoolId);
  if (disabledSchools.value.includes(schoolId)) {
    return; // 如果已禁用，则直接返回
  }
  // 清空之前的值以允许重新选择
  dataForm.deptId = "";
  dataForm.schoolUserId = "";
  dataForm.schoolName = "";
  // 查找选中的驾校对象
  const selectedSchool = schoolOptions.value.find(school => school.id === schoolId);
  if (selectedSchool) {
    dataForm.deptId = selectedSchool.id;
    dataForm.schoolUserId = selectedSchool.userId;
    dataForm.schoolName = selectedSchool.name;
  } else {
    console.warn("Selected school not found:", schoolId);
  }
};

const init = (id?: number,deptIds?: Number[]) => {
  visible.value = true;
  dataForm.id = "";

  // 重置表单数据
  if (dataFormRef.value) {
    dataFormRef.value.resetFields();
  }
// 如果有传入的id，执行获取详情逻辑
  if (id) {
    getInfo(id);
  }
  // 处理deptIds，这里可以根据业务逻辑进行适当的校验或筛选操作
  if (deptIds && deptIds.length > 0) {
    deptIds.forEach(deptId => {
      disabledSchools.value.push(deptId);
    })
  }
};

// 获取信息
const getInfo = (id: number) => {
  baseService.get("/dj/paymentmethodcfg/" + id).then((res) => {
    // 直接在res.data上删除不需要的属性
    delete res.data.updateBy;
    delete res.data.updateDate;
    delete res.data.updater;
    Object.assign(dataForm, res.data);
    const fetchedData = res.data
    // 将数字支付方式转换为中文
    dataForm.paymentMethodType = paymentMethodTypes[fetchedData.paymentMethodType];
    Object.assign(dataForm, { ...fetchedData, paymentMethodType: dataForm.paymentMethodType });
  });
};

// 表单提交
const dataFormSubmitHandle = () => {
  dataFormRef.value.validate((valid: boolean) => {
    if (!valid) {
      return false;
    }
    (!dataForm.id ? baseService.post : baseService.put)("/dj/paymentmethodcfg", dataForm).then((res) => {
      ElMessage.success({
        message: t("prompt.success"),
        duration: 500,
        onClose: () => {
          visible.value = false;
          emit("refreshDataList");
        }
      });
    });
  });
};

defineExpose({
  init
});
</script>
