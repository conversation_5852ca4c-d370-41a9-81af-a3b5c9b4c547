<template>
  <el-dialog v-model="visible" :title="$t('unbindingVehicle')" :close-on-click-modal="false" :close-on-press-escape="false">
    <el-form :model="dataForm" :rules="rules" ref="dataFormRef" @keyup.enter="dataFormSubmitHandle()" label-width="120px">
      <el-form-item label="车牌信息" prop="carId">
        <el-input v-model="dataForm.carId" placeholder="车牌信息" disabled></el-input>
      </el-form-item>
      <el-form-item label="车牌人姓名" prop="carUserName">
        <el-input v-model="dataForm.carUserName" placeholder="车牌人姓名" disabled></el-input>
      </el-form-item>
      <el-form-item label="车牌人电话" prop="carUserPhone">
        <el-input v-model="dataForm.carUserPhone" placeholder="车牌人电话" disabled></el-input>
      </el-form-item>
      <el-form-item label="驾校名称" prop="schoolName">
        <el-input v-model="dataForm.schoolName" placeholder="驾校名称" disabled></el-input>
      </el-form-item>
    </el-form>
    <template v-slot:footer>
      <el-button @click="visible = false">{{ $t("cancel") }}</el-button>
      <el-button type="primary" :disabled="!dataForm.carId" @click="dataFormSubmitHandle()">{{ $t("confirm") }}</el-button>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { reactive, ref } from "vue";
import baseService from "@/service/baseService";
import { useI18n } from "vue-i18n";
import { ElMessage } from "element-plus";
const { t } = useI18n();
const emit = defineEmits(["refreshDataList"]);

const visible = ref(false);
const dataFormRef = ref();

const dataForm = reactive({
  id: "",
  carType: "",
  carUserId: "",
  carUserPhone: "",
  carUserName: "",
  carId: "",
  schoolName: "",
  examinationRoomId: "",
  examinationRoomName: "",
  companyUserId: "",
  deptId: "",
  createDate: "",
  createBy: "",
  creator: "",
});

const rules = ref({
});

const init = (id?: number) => {
  visible.value = true;
  dataForm.id = "";

  // 重置表单数据
  if (dataFormRef.value) {
    dataFormRef.value.resetFields();
  }

  if (id) {
    getInfo(id);
  }
};

// 获取信息
const getInfo = (id: number) => {
  baseService.get("/dj/carinputinfo/" + id).then((res) => {
    Object.assign(dataForm, res.data);
  });
};

// 表单提交
const dataFormSubmitHandle = () => {
  dataFormRef.value.validate((valid: boolean) => {
    if (!valid) {
      return false;
    }
    baseService.post("/dj/carunbindrecord", dataForm).then((res) => {
      ElMessage.success({
        message: t("prompt.success"),
        duration: 500,
        onClose: () => {
          visible.value = false;
          emit("refreshDataList");
        }
      });
    });
  });
};

defineExpose({
  init
});
</script>
