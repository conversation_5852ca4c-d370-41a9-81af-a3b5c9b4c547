<template>
  <div class="mod-dj__examinationRental">
    <el-form :inline="true" :model="state.dataForm" @keyup.enter="state.getDataList()">
      <el-form-item>
        <el-input v-model="state.dataForm.carId" placeholder="车牌信息" clearable></el-input>
      </el-form-item>
      <el-form-item>
        <el-button @click="state.getDataList()">{{ $t("query") }}</el-button>
      </el-form-item>
      <el-form-item>
        <el-button type="info" @click="state.exportHandle()">{{ $t("export") }}</el-button>
      </el-form-item>
      <el-form-item>
        <el-button v-if="state.hasPermission('dj:carinputinfo:delete')" type="danger" @click="state.deleteHandle()">{{ $t("deleteBatch") }}</el-button>
      </el-form-item>
    </el-form>
    <el-table v-loading="state.dataListLoading" :data="state.dataList" border @selection-change="state.dataListSelectionChangeHandle" style="width: 100%">
      <el-table-column type="selection" header-align="center" align="center" width="50"></el-table-column>
      <el-table-column prop="carId" label="车牌信息" header-align="center" align="center"></el-table-column>
      <el-table-column prop="carStatus" label="车辆状态" header-align="center" align="center">
        <template v-slot="scope">
          <span :class="getStatusClass(scope.row.carStatus)">{{ getStatusText(scope.row.carStatus) }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="createBy" label="创建人" header-align="center" align="center"></el-table-column>
      <el-table-column prop="createDate" label="创建时间" header-align="center" align="center"></el-table-column>
      <el-table-column :label="$t('handle')" fixed="right" header-align="center" align="center" width="150">
        <template v-slot="scope">
          <el-button v-if="scope.row.carStatus === '0'" type="primary" link @click="addOrUpdateHandle(scope.row.id)">租借</el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination :current-page="state.page" :page-sizes="[10, 20, 50, 100]" :page-size="state.limit" :total="state.total" layout="total, sizes, prev, pager, next, jumper" @size-change="state.pageSizeChangeHandle" @current-change="state.pageCurrentChangeHandle"> </el-pagination>
    <!-- 弹窗, 新增 / 修改 -->
    <add-or-update :key="addKey" ref="addOrUpdateRef" @refreshDataList="state.getDataList"></add-or-update>
  </div>
</template>

<script lang="ts" setup>
import useView from "@/hooks/useView";
import { nextTick, reactive, ref, toRefs, watch } from "vue";
import AddOrUpdate from "./examinationRental-add-or-update.vue";

const view = reactive({
  getDataListURL: "/dj/carexaminationrentalrecord/examinationPage",
  getDataListIsPage: true,
  exportURL: "/dj/carexaminationrentalrecord/export",
  deleteURL: "/dj/carinputinfo",
  deleteIsBatch: true,
  dataForm: {
    carId: "",
    carUserName: "",
    carUserPhone: "",
    schoolName: "",
    carType: "",
    carStatus:"",
  }
});

const state = reactive({ ...useView(view), ...toRefs(view) });

// 定义状态类别映射
const carStatus = {
  0: '空闲中',
  1: '使用中',
};

// 获取状态文本
const getStatusText = (status: string | number) => {
  return carStatus[status] || '未知';
};

// 获取状态对应的CSS类名
const getStatusClass = (status: string | number) => {
  return `status-${status === '0' ? 'idle' : 'in-use'}`;
};

const addKey = ref(0);

const addOrUpdateRef = ref();
const addOrUpdateHandle = (id?: number) => {
  addKey.value++;
  nextTick(() => {
    addOrUpdateRef.value.init(id);
  });
};
</script>
<style scoped>.status-idle {
  color: green;
}

.status-in-use {
  color: red;
}
</style>
