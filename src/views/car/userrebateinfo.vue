<template>
  <div class="mod-dj__userrebateinfo">
    <el-form :inline="true" :model="state.dataForm" @keyup.enter="state.getDataList()">
      <el-form-item>
        <el-input v-model="state.dataForm.carId" placeholder="车牌信息" clearable></el-input>
      </el-form-item>
      <el-form-item>
        <el-input v-model="state.dataForm.carUserName" placeholder="车辆人姓名" clearable></el-input>
      </el-form-item>
      <el-form-item>
        <el-input v-model="state.dataForm.carUserPhone" placeholder="车辆人电话" clearable></el-input>
      </el-form-item>
      <el-form-item>
        <el-select v-model="state.dataForm.carType" placeholder="车辆类别">
          <el-option
            v-for="(value, key) in carTypes"
            :key="key"
            :label="value"
            :value="key"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-input v-model="state.dataForm.schoolName" placeholder="所属驾校名称" clearable></el-input>
      </el-form-item>
      <el-form-item>
        <el-select v-model="state.dataForm.whetherRebate" placeholder="是否返利">
          <el-option
            v-for="(value, key) in whetherRebates"
            :key="key"
            :label="value"
            :value="key"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-input v-model="state.dataForm.orderId" placeholder="订单id" clearable></el-input>
      </el-form-item>
      <el-form-item>
        <el-button @click="state.getDataList()">{{ $t("query") }}</el-button>
      </el-form-item>
      <el-form-item>
        <el-button type="info" @click="state.exportHandle()">{{ $t("export") }}</el-button>
      </el-form-item>
      <el-form-item>
        <el-button v-if="state.hasPermission('dj:userrebateinfo:save')" type="primary" @click="addOrUpdateHandle()">{{ $t("add") }}</el-button>
      </el-form-item>
      <el-form-item>
        <el-button v-if="state.hasPermission('dj:userrebateinfo:delete')" type="danger" @click="state.deleteHandle()">{{ $t("deleteBatch") }}</el-button>
      </el-form-item>
    </el-form>
    <el-table v-loading="state.dataListLoading" :data="state.dataList" border @selection-change="state.dataListSelectionChangeHandle" style="width: 100%">
      <el-table-column type="selection" header-align="center" align="center" width="50"></el-table-column>
      <el-table-column prop="carId" label="车牌信息" header-align="center" align="center"></el-table-column>
      <el-table-column prop="carUserName" label="车主姓名" header-align="center" align="center"></el-table-column>
      <el-table-column prop="carUserPhone" label="车主电话" header-align="center" align="center"></el-table-column>
      <el-table-column prop="carType" label="车辆类别" header-align="center" align="center" :formatter="formatCarType"></el-table-column>
      <el-table-column prop="schoolName" label="驾校名称" header-align="center" align="center"></el-table-column>
<!--      <el-table-column prop="examinationRoomName" label="考场名称" header-align="center" align="center"></el-table-column>-->
      <el-table-column prop="whetherRebate" label="是否返利" header-align="center" align="center" :formatter="formatWhetherRebate"></el-table-column>
      <el-table-column prop="userOpenId" label="返利打款账号" header-align="center" align="center"></el-table-column>
      <el-table-column prop="orderId" label="订单id" header-align="center" align="center"></el-table-column>
      <el-table-column prop="orderMoney" label="订单金额" header-align="center" align="center"></el-table-column>
      <el-table-column prop="shareMoney" label="返利金额" header-align="center" align="center"></el-table-column>
      <el-table-column prop="createDate" label="订单创建时间" header-align="center" align="center"></el-table-column>
      <el-table-column prop="updateDate" label="返利时间" header-align="center" align="center"></el-table-column>
      <el-table-column :label="$t('handle')" fixed="right" header-align="center" align="center" width="150">
        <template v-slot="scope">
          <el-button v-if="state.hasPermission('dj:userrebateinfo:update')" type="primary" link @click="addOrUpdateHandle(scope.row.id)">{{ $t("update") }}</el-button>
          <el-button v-if="state.hasPermission('dj:userrebateinfo:delete')" type="primary" link @click="state.deleteHandle(scope.row.id)">{{ $t("delete") }}</el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination :current-page="state.page" :page-sizes="[10, 20, 50, 100]" :page-size="state.limit" :total="state.total" layout="total, sizes, prev, pager, next, jumper" @size-change="state.pageSizeChangeHandle" @current-change="state.pageCurrentChangeHandle"> </el-pagination>
    <!-- 弹窗, 新增 / 修改 -->
    <add-or-update :key="addKey" ref="addOrUpdateRef" @refreshDataList="state.getDataList"></add-or-update>
  </div>
</template>

<script lang="ts" setup>
import useView from "@/hooks/useView";
import { nextTick, reactive, ref, toRefs, watch } from "vue";
import AddOrUpdate from "./userrebateinfo-add-or-update.vue";

const view = reactive({
  getDataListURL: "/dj/userrebateinfo/page",
  getDataListIsPage: true,
  exportURL: "/dj/userrebateinfo/export",
  deleteURL: "/dj/userrebateinfo",
  deleteIsBatch: true,
  dataForm: {
    carId: "",
    carUserName: "",
    carUserPhone: "",
    carType: "",
    schoolName: "",
    whetherRebate: "",
    orderId: "",
  }
});

const state = reactive({ ...useView(view), ...toRefs(view) });

// 定义车辆类别映射
const carTypes = {
  1: '教练车',
  2: '测试车',
  99: '模拟预约考试'
  // 添加其他支付方式...
};
// 添加格式化方法
const formatCarType = (row, column, cellValue) => {
  return carTypes[cellValue] || '未知';
};

//定义是否返利映射
const whetherRebates = {
  1: '已返利',
  0: '没返利',
  // 添加其他支付方式...
};
// 添加格式化方法
const formatWhetherRebate = (row, column, cellValue) => {
  return whetherRebates[cellValue] || '未知';
};

const addKey = ref(0);
const addOrUpdateRef = ref();
const addOrUpdateHandle = (id?: number) => {
  addKey.value++;
  nextTick(() => {
    addOrUpdateRef.value.init(id);
  });
};
</script>
