<template>
  <el-dialog v-model="visible" :title="$t('check')" :close-on-click-modal="false" :close-on-press-escape="false">
    <el-form :model="dataForm" :rules="rules" ref="dataFormRef" @keyup.enter="dataFormSubmitHandle(true)" label-width="120px">
      <el-form-item label="用户姓名" prop="carUserName">
        <el-input v-model="dataForm.carUserName" placeholder="用户姓名" disabled></el-input>
      </el-form-item>
      <el-form-item label="用户手机" prop="carUserPhone">
        <el-input v-model="dataForm.carUserPhone" placeholder="用户手机" disabled></el-input>
      </el-form-item>
      <el-form-item label="驾校名称" prop="schoolName">
        <el-input v-model="dataForm.schoolName" placeholder="驾校名称" disabled></el-input>
      </el-form-item>
      <el-form-item label="提现金额" prop="settAmount">
        <el-input v-model="dataForm.settAmount" placeholder="提现金额" disabled></el-input>
      </el-form-item>
      <el-form-item label="备注" prop="remake">
        <el-input v-model="dataForm.remake" placeholder="请输入备注"></el-input>
      </el-form-item>
    </el-form>

    <template v-slot:footer>
      <el-button @click="visible = false">{{ $t("cancel") }}</el-button>
      <el-button type="primary" @click="dataFormSubmitHandle(false)">{{ $t("disagree") }}</el-button>
      <el-button type="primary" @click="dataFormSubmitHandle(true)">{{ $t("agree") }}</el-button>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { reactive, ref } from "vue";
import baseService from "@/service/baseService";
import { useI18n } from "vue-i18n";
import { ElMessage } from "element-plus";
const { t } = useI18n();
const emit = defineEmits(["refreshDataList"]);

const visible = ref(false);
const dataFormRef = ref();

const dataForm = reactive({
  id: "",
  carUserId: "",
  carUserName: "",
  carUserPhone: "",
  schoolName: "",
  settAmount: "",
  remake: "",
  approved: true,
});
const flag = ref(false);

const rules = ref({
});

const init = (id?: number) => {
  visible.value = true;
  dataForm.id = "";

  // 重置表单数据
  if (dataFormRef.value) {
    dataFormRef.value.resetFields();
  }

  if (id) {
    getInfo(id);
  }
};

// 获取信息
const getInfo = (id: number) => {
  baseService.get("/dj/withdrawalapprovalrecord/" + id).then((res) => {
    Object.assign(dataForm, res.data);
  });
};

// 表单提交
const dataFormSubmitHandle = (approved: boolean) => {
  dataFormRef.value.validate((valid: boolean) => {
    if (!valid) {
      return false;
    }
    if(!flag.value){
      flag.value = true;
      dataForm.approved = approved;
      console.log("dataForm",dataForm.valueOf());
      baseService.put("/dj/withdrawalapprovalrecord/check", dataForm).then((res) => {
        ElMessage.success({
          message: t("prompt.success"),
          duration: 500,
          onClose: () => {
            visible.value = false;
            flag.value = true;
            emit("refreshDataList");
          }
        });
      });
    }
  });
};

defineExpose({
  init
});
</script>
