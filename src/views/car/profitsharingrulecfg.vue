<template>
  <div class="mod-dj__profitsharingrulecfg">
    <el-form :inline="true" :model="state.dataForm" @keyup.enter="state.getDataList()">
      <el-form-item>
        <el-select v-model="state.dataForm.carType" placeholder="车辆类别">
          <el-option
            v-for="(value, key) in carTypes"
            :key="key"
            :label="value"
            :value="key"
          ></el-option>
        </el-select>
      </el-form-item>
<!--      <el-form-item>-->
<!--        <el-input v-model="state.dataForm.schoolName" placeholder="考场名称" clearable></el-input>-->
<!--      </el-form-item>-->
      <el-form-item>
        <el-button @click="state.getDataList()">{{ $t("query") }}</el-button>
      </el-form-item>
      <el-form-item>
        <el-button type="info" @click="state.exportHandle()">{{ $t("export") }}</el-button>
      </el-form-item>
      <el-form-item>
        <el-button v-if="state.hasPermission('dj:profitsharingrulecfg:save')" type="primary" @click="addOrUpdateHandle()">{{ $t("add") }}</el-button>
      </el-form-item>
      <el-form-item>
        <el-button v-if="state.hasPermission('dj:profitsharingrulecfg:delete')" type="danger" @click="state.deleteHandle()">{{ $t("deleteBatch") }}</el-button>
      </el-form-item>
    </el-form>
    <el-table v-loading="state.dataListLoading" :data="state.dataList" border @selection-change="state.dataListSelectionChangeHandle" style="width: 100%">
      <el-table-column type="selection" header-align="center" align="center" width="50"></el-table-column>
      <el-table-column prop="ruleConfigParam" label="分利说明" header-align="center" align="center">
        <template v-slot="scope">
          <span v-if="scope.row.ruleConfigParam && isJsonString(scope.row.ruleConfigParam)">
            {{ formatRuleDescription(scope.row) }}
          </span>
          <span v-else>无效的规则描述</span>
        </template>
      </el-table-column>
      <el-table-column prop="carType" label="车辆类别" header-align="center" align="center" :formatter="formatCarType"></el-table-column>
<!--      <el-table-column prop="schoolName" label="考场名称" header-align="center" align="center"></el-table-column>-->
      <el-table-column prop="createBy" label="创建人" header-align="center" align="center"></el-table-column>
      <el-table-column prop="createDate" label="创建时间" header-align="center" align="center"></el-table-column>
      <el-table-column prop="updateBy" label="更新人" header-align="center" align="center"></el-table-column>
      <el-table-column prop="updateDate" label="更新时间" header-align="center" align="center"></el-table-column>
      <el-table-column :label="$t('handle')" fixed="right" header-align="center" align="center" width="150">
        <template v-slot="scope">
          <el-button v-if="state.hasPermission('dj:profitsharingrulecfg:update')" type="primary" link @click="addOrUpdateHandle(scope.row.id)">{{ $t("update") }}</el-button>
          <el-button v-if="state.hasPermission('dj:profitsharingrulecfg:delete')" type="primary" link @click="state.deleteHandle(scope.row.id)">{{ $t("delete") }}</el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination :current-page="state.page" :page-sizes="[10, 20, 50, 100]" :page-size="state.limit" :total="state.total" layout="total, sizes, prev, pager, next, jumper" @size-change="state.pageSizeChangeHandle" @current-change="state.pageCurrentChangeHandle"> </el-pagination>
    <!-- 弹窗, 新增 / 修改 -->
    <add-or-update :key="addKey" ref="addOrUpdateRef" @refreshDataList="state.getDataList"></add-or-update>
  </div>
</template>

<script lang="ts" setup>
import useView from "@/hooks/useView";
import { nextTick, reactive, ref, toRefs, watch } from "vue";
import AddOrUpdate from "./profitsharingrulecfg-add-or-update.vue";

const view = reactive({
  getDataListURL: "/dj/profitsharingrulecfg/page",
  getDataListIsPage: true,
  exportURL: "/dj/profitsharingrulecfg/export",
  deleteURL: "/dj/profitsharingrulecfg",
  deleteIsBatch: true,
  dataForm: {
    carType: "",
    schoolName: "",
  }
});

const state = reactive({ ...useView(view), ...toRefs(view) });

// 新增的方法，检查字符串是否为有效的 JSON
function isJsonString(str) {
  try {
    JSON.parse(str);
    return true;
  } catch (e) {
    return false;
  }
}

// 格式化收费规则描述
function formatRuleDescription(row) {
  const config = JSON.parse(row.ruleConfigParam);
  if (!config || !config.comMoney || !config.coachMoney) return '无效的规则描述';

  let description = `公司分利比例: ${config.comMoney}%，教练分利比例: ${config.coachMoney}%`;
  return description;
}

// 定义车辆类别映射
const carTypes = {
  1: '教练车',
  2: '测试车',
  // 添加其他支付方式...
};
// 添加格式化方法
const formatCarType = (row, column, cellValue) => {
  return carTypes[cellValue] || '未知';
};

const addKey = ref(0);
const addOrUpdateRef = ref();
const addOrUpdateHandle = (id?: number) => {
  addKey.value++;
  nextTick(() => {
    addOrUpdateRef.value.init(id);
  });
};
</script>
