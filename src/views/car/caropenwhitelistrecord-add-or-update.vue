<template>
  <el-dialog v-model="visible" :title="!dataForm.id ? $t('add') : $t('update')" :close-on-click-modal="false" :close-on-press-escape="false">
    <el-form :model="dataForm" :rules="rules" ref="dataFormRef" @keyup.enter="dataFormSubmitHandle()" label-width="120px">
      <el-form-item label="车主电话" prop="carUserPhone">
        <el-input v-model="dataForm.carUserPhone" placeholder="车主电话" readonly>
          <template #append>
            <el-button :icon="Search"  @click="addUserHandle()" />
          </template>
        </el-input>
        <!--        <el-button type="primary" @click="fetchOwnerInfo">{{ $t("query") }}</el-button>-->
      </el-form-item>
      <el-form-item label="车主姓名" prop="carUserName">
        <el-input v-model="dataForm.carUserName" placeholder="车主姓名" disabled></el-input>
      </el-form-item>
      <el-form-item label="车牌信息" prop="carId">
        <el-input v-model="dataForm.carId" placeholder="车牌信息" disabled></el-input>
      </el-form-item>

      <coachselect ref="customersignRef" type="coach" @change="handleSelectUser"></coachselect>
<!--      <el-form-item label="考场名称" prop="schoolName" :rules="rules.examinationRoomId">-->
<!--        <el-select v-model="dataForm.examinationRoomId" placeholder="请选择考场名称" @change="handleSchoolChange">-->
<!--          <el-option-->
<!--            v-for="item in schoolOptions"-->
<!--            :key="item.id"-->
<!--            :label="item.name"-->
<!--            :value="item.id"-->
<!--          ></el-option>-->
<!--        </el-select>-->
<!--      </el-form-item>-->

      <el-form-item label="开始时间" prop="startWhitelistDate">
        <el-date-picker
          v-model="dataForm.startWhitelistDate"
          type="datetime"
          format="YYYY-MM-DD HH:mm:ss"
          value-format="YYYY-MM-DD HH:mm:ss"
          :default-time="new Date()"
          :disabled-date="disabledDate"
          :disabled-hours="disabledHours"
          :disabled-minutes="disabledMinutes"
          :disabled-seconds="disabledSeconds"
          placeholder="选择开始时间" ></el-date-picker>
      </el-form-item>

      <el-form-item label="结束时间" prop="endWhitelistDate">
        <el-date-picker
          v-model="dataForm.endWhitelistDate"
          type="datetime"
          :default-time="new Date()"
          format="YYYY-MM-DD HH:mm:ss"
          value-format="YYYY-MM-DD HH:mm:ss"
          :disabled-date="disabledDate"
          :disabled-hours="disabledHours"
          :disabled-minutes="disabledMinutes"
          :disabled-seconds="disabledSeconds"
          placeholder="选择结束时间" ></el-date-picker>
      </el-form-item>
    </el-form>
    <template v-slot:footer>
      <el-button @click="visible = false">{{ $t("cancel") }}</el-button>
      <el-button type="primary" @click="dataFormSubmitHandle()">{{ $t("confirm") }}</el-button>
    </template>
  </el-dialog>
  <el-dialog
    v-model="dataForm.ownersDialogVisible"
    title="选择车主"
    width="30%"
    destroy-on-close
  >
    <el-select
      v-model="selectedOwner"
      placeholder="请选择车主"
      @change="onOwnerSelect"
    >
      <el-option
        v-for="owner in dataForm.owners" :key="owner.id" :label="`车牌：${owner.carId}，姓名：${owner.carUserName}，电话：${owner.carUserPhone}`" :value="owner.id">
      </el-option>
    </el-select>

    <template #footer>
    <span class="dialog-footer">
      <el-button @click="dataForm.ownersDialogVisible = false">取消</el-button>
      <el-button type="primary" @click="selectAndCloseOwnerDialog" :disabled="!selectedOwner">确定</el-button>
    </span>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { onMounted, reactive, ref } from "vue";
import baseService from "@/service/baseService";
import { useI18n } from "vue-i18n";
import { ElMessage } from "element-plus";
import coachselect from "./coachCarselect.vue";
import { Search } from "@element-plus/icons-vue";
import Template from "@/views/devtools/template.vue";
import { dayjs } from 'element-plus';
const { t } = useI18n();
const emit = defineEmits(["refreshDataList"]);

const visible = ref(false);
const dataFormRef = ref();
const schoolOptions = ref([]); // 用于存储驾校选项
const customersignRef = ref();
const dataForm = reactive({
  id: "",
  carId: "",
  carUserName: "",
  carUserId:"",
  schoolName: "",
  carUserPhone: "",
  openWhitelistNumbers: "",
  startWhitelistDate: "",
  endWhitelistDate: "",
  deptId: "",
  examinationRoomId: "",
  examinationRoomName:"",
  selectedOwner: [], // 新增的属性，用于存储用户选择的车主
  owners: [], // 新增的属性，用于存储车主列表
  ownersDialogVisible: false, // 新增的属性，用于控制车主列表弹窗的显示隐藏
});

const checkExaminationRoomId = (rule: any, value: number, callback: any) => {
  if (!dataForm.examinationRoomId){
    callback(new Error(t('请选择考场')));
  }  else {
    callback();
  }
};

const checkStartWhitelistDate = (rule: any, value: number, callback: any) => {
  if(!value){
    callback(new Error(t('请选择开始时间')));
  } else {
    if (dataForm.endWhitelistDate && value) {
      let start = new Date(value).getTime();
      let end = new Date(dataForm.endWhitelistDate).getTime();
      if (start >= end) {
        callback(new Error(t('结束时间要晚于开始时间')));
      }
      callback();
    }
  }
};
const disabledDate = (time:any)=>{
  return time.getTime() < Date.now() - 8.64e7;
};

const makeRange = (start: number, end: number) => {
  const result: number[] = []
  for (let i = start; i <= end; i++) {
    result.push(i)
  }
  return result
}
// 限制小时
const disabledHours = () => {
  return makeRange(0,new Date().getHours()-1);
};

// 限制分钟
const disabledMinutes = () => {
  return makeRange(0,new Date().getMinutes()-1);
};


// 限制秒
const disabledSeconds = () => {
  return makeRange(0,new Date().getSeconds());
};


const checkEndWhitelistDate = (rule: any, value: number, callback: any) => {
  if(!value){
    callback(new Error(t('请选择结束时间')));
  } else {
    if (dataForm.startWhitelistDate && value) {
      let start = new Date(dataForm.startWhitelistDate).getTime();
      let end = new Date(value).getTime();
      if (start >= end) {
        callback(new Error(t('结束时间要晚于开始时间')));
      }
      callback();
    }
  }

};


const rules = ref({
  examinationRoomId:[
    {validator: checkExaminationRoomId, trigger: 'blur' }
  ],
  carUserPhone: [
    { required: true, message: t("请选择教练"), trigger: "blur" },
    { pattern: /^1[3-9]\d{9}$/, message: t("请输入正确的手机号格式"), trigger: "blur" }
  ],
  openWhitelistNumbers: [
    { required: true, message: t("请输入小时数"), trigger: "blur" },
  ],
  carUserName: [
    { required: true, message: t('请选择教练'), trigger: 'change' },
  ],
  startWhitelistDate:[
    {validator: checkStartWhitelistDate, trigger: 'blur' }
  ],
  endWhitelistDate:[
    {validator: checkEndWhitelistDate, trigger: 'blur' }
  ],

});

const selectedOwner = ref("");

const disabledSchools = ref<number[]>([]);

onMounted(() => {
  fetchSchoolList(); // 页面加载时获取驾校列表
});

const fetchSchoolList = async () => {
  try {
    const response = await baseService.get('/dj/examinationroominfo/getExaminationRoomInfo'); // 假设这是获取驾校列表的接口
    schoolOptions.value = response.data.map(school => ({ id: school.id, name: school.examinationRoomName,}));
  } catch (error) {
    ElMessage.error({ message: t("prompt.errorFetchingSchoolList"), duration: 3000 });
  }
};

const onOwnerSelect = (value) => {
  const selectedOwner = dataForm.owners.find(owner => owner.id === value);
  if (selectedOwner) {
    dataForm.selectedOwner.carUserId = selectedOwner.carUserId;
    dataForm.selectedOwner.carUserName = selectedOwner.carUserName;
    dataForm.selectedOwner.carUserPhone = selectedOwner.carUserPhone;
    dataForm.selectedOwner.schoolName = selectedOwner.schoolName;
    dataForm.selectedOwner.carId = selectedOwner.carId;
  } else {
    ElMessage.error({ message: t("请选择有效的车主信息"), duration: 2000 });
  }
};

const selectAndCloseOwnerDialog = () => {
  if (dataForm.selectedOwner) {
    dataForm.carUserId = dataForm.selectedOwner.carUserId;
    dataForm.carUserName = dataForm.selectedOwner.carUserName;
    dataForm.carUserPhone = dataForm.selectedOwner.carUserPhone;
    dataForm.schoolName = dataForm.selectedOwner.schoolName;
    dataForm.carId = dataForm.selectedOwner.carId;
    dataForm.ownersDialogVisible = false;
  } else {
    ElMessage.error({ message: t("请选择有效的车主信息"), duration: 2000 });
  }
};

// 当考场名称改变时，可以在这里处理其他逻辑，比如根据学校ID获取更多信息
const handleSchoolChange = (schoolId: string) => {
  if (disabledSchools.value.includes(schoolId)) {
    return; // 如果已禁用，则直接返回
  }
  // 清空之前的值以允许重新选择
  dataForm.examinationRoomId = "";
  dataForm.examinationRoomName = "";
  // 查找选中的驾校对象
  const selectedSchool = schoolOptions.value.find(school => school.id === schoolId);
  if (selectedSchool) {
    dataForm.examinationRoomId = selectedSchool.id;
    dataForm.examinationRoomName = selectedSchool.name;
  } else {
    console.warn("Selected school not found:", schoolId);
  }
};

const fetchOwnerInfo = async () => {
  if (!dataForm.carUserPhone.trim()) {
    ElMessage.warning({ message: t("请输入手机号"), duration: 2000 });
    return;
  }
  const params = {
    userPhone: dataForm.carUserPhone
  };
  const response = await baseService.post("/dj/carinputinfo/getCoachCarInfo",params);
  dataForm.owners = response.data;
  if (response.data.length === 1) { // 如果只有一个结果，直接填充信息
    const singleOwner = response.data[0];
    dataForm.carUserId = singleOwner.carUserId;
    dataForm.carUserName = singleOwner.carUserName;
    dataForm.carUserPhone = singleOwner.carUserPhone;
    dataForm.schoolName = singleOwner.schoolName;
    dataForm.carId = singleOwner.carId;

    // 直接关闭车主选择对话框，因为已自动填充
    dataForm.ownersDialogVisible = false;

    // 这里可以根据需要决定是否还需要展示一个成功的提示消息
    ElMessage.success({ message: t("已自动选择唯一匹配的车主信息"), duration: 2000 });
    //dataForm.ownersDialogVisible = true;
  }else if (response.data.length > 1){
    dataForm.ownersDialogVisible = true;
  } else {
    ElMessage.error(t("用户尚未注册"));
  }
};

const init = (id?: number) => {
  visible.value = true;
  dataForm.id = "";

  // 重置表单数据
  if (dataFormRef.value) {
    dataFormRef.value.resetFields();
  }

  if (id) {
    getInfo(id);
  }
};

//选择客户
const addUserHandle = ()=>{
  customersignRef.value.init();
};
const handleSelectUser = (data: any)=>{
  dataForm.owners = data[0];
  dataForm.carUserPhone = data[0].carUserPhone;
  dataForm.carUserId = data[0].carUserId; // 清空车主ID
  dataForm.carUserName = data[0].carUserName; // 清空车主姓名
  dataForm.schoolName = data[0].schoolName; // 如果适用，清空考场名称
  dataForm.carId = data[0].carId; // 如果适用，清空考场名称
  dataFormRef.value.clearValidate(["carUserPhone"]);
  dataFormRef.value.clearValidate(["carUserName"]);
  // dataForm.userList = JSON.parse(JSON.stringify(data));
};

// 获取信息
const getInfo = (id: number) => {
  baseService.get("/dj/caropenwhitelistrecord/" + id).then((res) => {
    Object.assign(dataForm, res.data);
  });
};

// 表单提交
const dataFormSubmitHandle = () => {
  dataFormRef.value.validate((valid: boolean) => {
    if (!valid) {
      return false;
    }
    //转换时间格式为 yyyy-MM-dd HH:mm:ss
    (!dataForm.id ? baseService.post : baseService.put)("/dj/caropenwhitelistrecord", dataForm).then((res) => {
      ElMessage.success({
        message: t("prompt.success"),
        duration: 500,
        onClose: () => {
          visible.value = false;
          emit("refreshDataList");
        }
      });
    });
  });
};

defineExpose({
  init
});
</script>
