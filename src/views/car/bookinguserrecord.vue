<template>
  <div class="mod-dj__bookinguserrecord">
    <el-form :inline="true" :model="state.dataForm" @keyup.enter="state.getDataList()">
      <el-form-item label="">
        <el-date-picker
          v-model="state.warehousingDate"
          type="datetimerange"
          format="YYYY-MM-DD HH:mm:ss"
          value-format="YYYY-MM-DD HH:mm:ss"
          :default-time="new Date()"
          start-placeholder="开始时间"
          end-placeholder="结束时间"></el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button @click="handleSearch">{{ $t("query") }}</el-button>
      </el-form-item>
      <el-form-item>
        <el-button type="info" @click="state.exportHandle()">{{ $t("export") }}</el-button>
      </el-form-item>
    </el-form>
    <el-table v-loading="state.dataListLoading" :data="state.dataList" border @selection-change="state.dataListSelectionChangeHandle" style="width: 100%">
      <el-table-column type="selection" header-align="center" align="center" width="50"></el-table-column>
<!--      <el-table-column prop="id" label="主键" header-align="center" align="center"></el-table-column>-->
<!--      <el-table-column prop="bookingCfgId" label="预约设置表ID" header-align="center" align="center"></el-table-column>-->
<!--      <el-table-column prop="carUserId" label="教练用户ID" header-align="center" align="center"></el-table-column>-->
      <el-table-column prop="carUserName" label="教练用户姓名" header-align="center" align="center"></el-table-column>
      <el-table-column prop="carUserPhone" label="教练用户手机" header-align="center" align="center"></el-table-column>
      <el-table-column prop="bookingType" label="类型" header-align="center" align="center">
        <template v-slot="scope">
         <span>{{scope.row.bookingType=='1'?'C1':'C2'}}</span>
        </template>
      </el-table-column>
      <el-table-column prop="userStatus" label="预约状态" header-align="center" align="center">
        <template v-slot="scope">
          <el-tag v-if="scope.row.userStatus=='0'" type="warning"  >未使用</el-tag>
          <el-tag v-else type="success"  >已使用</el-tag>
        </template>
      </el-table-column>
<!--      <el-table-column prop="deptId" label="公司id" header-align="center" align="center"></el-table-column>-->
<!--      <el-table-column prop="companyUserId" label="公司用户id" header-align="center" align="center"></el-table-column>-->
<!--      <el-table-column prop="creator" label="创建人id" header-align="center" align="center"></el-table-column>-->
      <el-table-column prop="createBy" label="创建人" header-align="center" align="center"></el-table-column>
      <el-table-column prop="createDate" label="创建时间" header-align="center" align="center"></el-table-column>
<!--      <el-table-column prop="updater" label="更新人id" header-align="center" align="center"></el-table-column>-->
      <el-table-column prop="updateBy" label="更新人" header-align="center" align="center"></el-table-column>
      <el-table-column prop="updateDate" label="更新时间" header-align="center" align="center"></el-table-column>
<!--      <el-table-column :label="$t('handle')" fixed="right" header-align="center" align="center" width="150">-->
<!--        <template v-slot="scope">-->
<!--          <el-button v-if="state.hasPermission('dj:bookinguserrecord:update')" type="primary" link @click="addOrUpdateHandle(scope.row.id)">{{ $t("update") }}</el-button>-->
<!--          <el-button v-if="state.hasPermission('dj:bookinguserrecord:delete')" type="primary" link @click="state.deleteHandle(scope.row.id)">{{ $t("delete") }}</el-button>-->
<!--        </template>-->
<!--      </el-table-column>-->
    </el-table>
    <el-pagination :current-page="state.page" :page-sizes="[10, 20, 50, 100]" :page-size="state.limit" :total="state.total" layout="total, sizes, prev, pager, next, jumper" @size-change="state.pageSizeChangeHandle" @current-change="state.pageCurrentChangeHandle"> </el-pagination>
    <!-- 弹窗, 新增 / 修改 -->
    <add-or-update :key="addKey" ref="addOrUpdateRef" @refreshDataList="state.getDataList"></add-or-update>
  </div>
</template>

<script lang="ts" setup>
import useView from "@/hooks/useView";
import { nextTick, reactive, ref, toRefs, watch } from "vue";
import AddOrUpdate from "./bookinguserrecord-add-or-update.vue";

const view = reactive({
  getDataListURL: "/dj/bookinguserrecord/page",
  getDataListIsPage: true,
  exportURL: "/dj/bookinguserrecord/export",
  deleteURL: "/dj/bookinguserrecord",
  deleteIsBatch: true,
  dataForm: {
    startTime:'',
    endTime:''
  },
  warehousingDate:''
});

const state = reactive({ ...useView(view), ...toRefs(view) });


const addKey = ref(0);
const addOrUpdateRef = ref();
const addOrUpdateHandle = (id?: number) => {
  addKey.value++;
  nextTick(() => {
    addOrUpdateRef.value.init(id);
  });
};

const handleSearch = () => {
  if(state.warehousingDate){
    state.dataForm.startTime = state.warehousingDate[0];
    state.dataForm.endTime = state.warehousingDate[1];
  } else {
    state.dataForm.startTime = '';
    state.dataForm.endTime = '';
  }
  state.getDataList();
};
</script>
