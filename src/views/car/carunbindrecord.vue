<template>
  <div class="mod-dj__carunbindrecord">
    <el-form :inline="true" :model="state.dataForm" @keyup.enter="state.getDataList()">
      <el-form-item>
        <el-input v-model="state.dataForm.carUserPhone" placeholder="车牌人电话" clearable></el-input>
      </el-form-item>
      <el-form-item>
        <el-input v-model="state.dataForm.carUserName" placeholder="车牌人姓名" clearable></el-input>
      </el-form-item>
      <el-form-item>
        <el-input v-model="state.dataForm.carId" placeholder="车牌信息" clearable></el-input>
      </el-form-item>
      <el-form-item>
        <el-input v-model="state.dataForm.schoolName" placeholder="驾校名称" clearable></el-input>
      </el-form-item>
      <el-form-item>
        <el-button @click="state.getDataList()">{{ $t("query") }}</el-button>
      </el-form-item>
      <el-form-item>
        <el-button type="info" @click="state.exportHandle()">{{ $t("export") }}</el-button>
      </el-form-item>
      <el-form-item>
        <el-button v-if="state.hasPermission('dj:carunbindrecord:save')" type="primary" @click="addOrUpdateHandle()">{{ $t("add") }}</el-button>
      </el-form-item>
      <el-form-item>
        <el-button v-if="state.hasPermission('dj:carunbindrecord:delete')" type="danger" @click="state.deleteHandle()">{{ $t("deleteBatch") }}</el-button>
      </el-form-item>
    </el-form>
    <el-table v-loading="state.dataListLoading" :data="state.dataList" border @selection-change="state.dataListSelectionChangeHandle" style="width: 100%">
      <el-table-column type="selection" header-align="center" align="center" width="50"></el-table-column>
      <el-table-column prop="carUserPhone" label="车牌人电话" header-align="center" align="center"></el-table-column>
      <el-table-column prop="carUserName" label="车牌人姓名" header-align="center" align="center"></el-table-column>
      <el-table-column prop="carId" label="车牌信息" header-align="center" align="center"></el-table-column>
      <el-table-column prop="schoolName" label="驾校名称" header-align="center" align="center"></el-table-column>
      <el-table-column prop="createDate" label="解绑时间" header-align="center" align="center"></el-table-column>
      <el-table-column prop="createBy" label="解绑人" header-align="center" align="center"></el-table-column>
      <el-table-column :label="$t('handle')" fixed="right" header-align="center" align="center" width="150">
        <template v-slot="scope">
          <el-button v-if="state.hasPermission('dj:carunbindrecord:update')" type="primary" link @click="addOrUpdateHandle(scope.row.id)">{{ $t("update") }}</el-button>
          <el-button v-if="state.hasPermission('dj:carunbindrecord:delete')" type="primary" link @click="state.deleteHandle(scope.row.id)">{{ $t("delete") }}</el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination :current-page="state.page" :page-sizes="[10, 20, 50, 100]" :page-size="state.limit" :total="state.total" layout="total, sizes, prev, pager, next, jumper" @size-change="state.pageSizeChangeHandle" @current-change="state.pageCurrentChangeHandle"> </el-pagination>
    <!-- 弹窗, 新增 / 修改 -->
    <add-or-update :key="addKey" ref="addOrUpdateRef" @refreshDataList="state.getDataList"></add-or-update>
  </div>
</template>

<script lang="ts" setup>
import useView from "@/hooks/useView";
import { nextTick, reactive, ref, toRefs, watch } from "vue";
import AddOrUpdate from "./carunbindrecord-add-or-update.vue";

const view = reactive({
  getDataListURL: "/dj/carunbindrecord/page",
  getDataListIsPage: true,
  exportURL: "/dj/carunbindrecord/export",
  deleteURL: "/dj/carunbindrecord",
  deleteIsBatch: true,
  dataForm: {
    carUserPhone: "",
    carUserName: "",
    carId: "",
    schoolName: "",
  }
});

const state = reactive({ ...useView(view), ...toRefs(view) });


const addKey = ref(0);
const addOrUpdateRef = ref();
const addOrUpdateHandle = (id?: number) => {
  addKey.value++;
  nextTick(() => {
    addOrUpdateRef.value.init(id);
  });
};
</script>
