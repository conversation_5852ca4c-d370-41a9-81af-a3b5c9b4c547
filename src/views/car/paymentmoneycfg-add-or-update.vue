<template>
  <el-dialog v-model="visible" :title="!dataForm.id ? $t('add') : $t('update')" :close-on-click-modal="false" :close-on-press-escape="false">
    <el-form :model="dataForm" :rules="rules" ref="dataFormRef" @keyup.enter="dataFormSubmitHandle()" label-width="120px">
<!--      <el-form-item label="配置类型" prop="dataType">-->
<!--        <el-select v-model="dataType" placeholder="请选配置类型">-->
<!--          <el-option label="通用配置" :value="1" selected-label="1"></el-option>-->
<!--          <el-option label="个性化配置" :value="0" selected-label="0"></el-option>-->
<!--        </el-select>-->
<!--      </el-form-item>-->
      <el-form-item label="车牌" prop="carId" v-if="props.type==1">
        <el-input v-model="dataForm.carId" readonly placeholder="选择车牌" :disabled="dataForm.id">
          <template #append>
            <el-button :icon="Search"  @click="addUserHandle()" />
          </template>
        </el-input>
        <!--          <el-button type="primary" @click="fetchOwnerInfo">{{ $t("query") }}</el-button>-->
      </el-form-item>

      <el-form-item label="车辆类别" prop="carType">
        <el-select v-model="dataForm.carType" placeholder="请选择车辆类别" :disabled="(props.type==1?true:false)||(dataForm.id)">
          <el-option
            v-for="(value, key) in carTypes"
            :key="key"
            :label="value"
            :value="key"
            :selected-label="value"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="收费类型" prop="chargeType">
        <el-select v-model="dataForm.ruleConfigParam.chargeType" placeholder="请选择收费类型">
          <el-option label="按小时收费" :value="1"></el-option>
          <!--          <el-option label="按天收费" :value="2"></el-option>-->
        </el-select>
      </el-form-item>

<!--      <el-form-item label="返利类型" prop="chargeType">-->
<!--        <el-select v-model="dataForm.ruleConfigParam.rebateType" placeholder="请选择收费类型">-->
<!--&lt;!&ndash;          <el-option label="按百分比返利" :value="1"></el-option>&ndash;&gt;-->
<!--          <el-option label="按小时返利" :value="2"></el-option>-->
<!--        </el-select>-->
<!--      </el-form-item>-->
<!--      <template v-if="dataForm.ruleConfigParam.rebateType == 1">-->
<!--        <el-form-item label="收费规则" prop="ruleConfigParam.percentObj.carSpeedMoney" :rules="rules.carSpeedMoney">-->
<!--          <el-input-number :controls="false" v-model="dataForm.ruleConfigParam.percentObj.carSpeedMoney" :min="0" placeholder="请输入每小时收费金额" clearable style="width: 154px; margin-left: 5px"/>-->
<!--          <span class="margin-left-xs margin-right-xs">元/小时</span>-->
<!--        </el-form-item>-->
<!--        <el-form-item label="考场分利" prop="ruleConfigParam.percentObj.rebateComMoney" :rules="rules.rebateComMoney">-->
<!--          <el-input-number :controls="false" v-model="dataForm.ruleConfigParam.percentObj.rebateComMoney" placeholder="请输入考场分利比例" :precision="0" controls-position="right" style="width: 50%"/>-->
<!--          <span class="margin-left-xs">%</span>-->
<!--        </el-form-item>-->
<!--        <el-form-item label="教练分利" prop="ruleConfigParam.percentObj.rebateCoachMoney" :rules="rules.rebateCoachMoney">-->
<!--          <el-input-number :controls="false" v-model="dataForm.ruleConfigParam.percentObj.rebateCoachMoney" placeholder="请输入教练分利比例" :precision="0" controls-position="right" style="width: 50%"/>-->
<!--          <span class="margin-left-xs">%</span>-->
<!--        </el-form-item>-->
<!--      </template>-->
        <el-form-item label="考场收费" prop="ruleConfigParam.hourObj.carSpeedMoney" :rules="rules.hCarSpeedMoney">
          <el-input-number :controls="false" v-model="dataForm.ruleConfigParam.hourObj.carSpeedMoney" :min="0" placeholder="请输入每小时收费金额" clearable style="width: 154px; margin-left: 5px"/>
          <span class="margin-left-xs margin-right-xs">元/小时</span>
        </el-form-item>
<!--        <el-form-item label="考场收费" prop="ruleConfigParam.hourObj.rebateComMoney" :rules="rules.hRebateComMoney">-->
<!--          <el-input-number :controls="false" v-model="dataForm.ruleConfigParam.hourObj.rebateComMoney" placeholder="请输入考场每小时分利" :precision="0" controls-position="right" style="width: 50%"/>-->
<!--          <span class="margin-left-xs">元/每小时</span>-->
<!--        </el-form-item>-->
<!--        <el-form-item label="教练分利" prop="ruleConfigParam.hourObj.rebateCoachMoney" :rules="rules.hRebateCoachMoney">-->
<!--          <el-input-number :controls="false" v-model="dataForm.ruleConfigParam.hourObj.rebateCoachMoney" placeholder="请输入教练每小时分利" :precision="0" controls-position="right" style="width: 50%"/>-->
<!--          <span class="margin-left-xs">元/每小时</span>-->
<!--        </el-form-item>-->
<!--      <el-form-item label="考场名称" prop="schoolName" :rules="rules.examinationRoomId">-->
<!--        <el-select v-model="dataForm.examinationRoomId" placeholder="请选择考场名称" @change="handleSchoolChange">-->
<!--          <el-option-->
<!--            v-for="item in schoolOptions"-->
<!--            :key="item.id"-->
<!--            :label="item.name"-->
<!--            :value="item.id"-->
<!--          ></el-option>-->
<!--        </el-select>-->
<!--      </el-form-item>-->
      <coachselect ref="customersignRef" type="all" @change="handleSelectUser"></coachselect>
    </el-form>
    <template v-slot:footer>
      <el-button @click="visible = false">{{ $t("cancel") }}</el-button>
      <el-button type="primary" @click="dataFormSubmitHandle()">{{ $t("confirm") }}</el-button>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { onMounted, reactive, ref, watch } from "vue";
import baseService from "@/service/baseService";
import { useI18n } from "vue-i18n";
import { ElMessage } from "element-plus";
import Template from "@/views/devtools/template.vue";
import { Search } from "@element-plus/icons-vue";
import coachselect from "./coachCarselect.vue";
const { t } = useI18n();
const emit = defineEmits(["refreshDataList"]);
const visible = ref(false);
const dataFormRef = ref();
const customersignRef = ref();
const dataList = ref([]);

const props = defineProps({
  type: String
});

const dataForm = reactive({
  carId:'',
  id: null,
  carType: "",
  ruleConfigParam: {
    rebateType: 2,
    chargeType:1,
    percentObj:{
      carSpeedMoney: 0,
      rebateComMoney: 0,
      rebateCoachMoney: 0
    },
    hourObj:{
      carSpeedMoney: 0,
      rebateComMoney: 0,
      rebateCoachMoney: 0
    }
  },
  cfgStatus:"使用中",
});

// 收费规则
const checkCarSpeedMoney = (rule: any, value: number, callback: any) => {
  let pattern = /(^[1-9]\d*$)/;
  if (!dataForm.ruleConfigParam.percentObj.carSpeedMoney&&dataForm.ruleConfigParam.percentObj.carSpeedMoney!=0){
    callback(new Error(t('请填写收费规则')));
  }  else if(!pattern.test(dataForm.ruleConfigParam.percentObj.carSpeedMoney.toString())){
    callback(new Error(t('请输入大于0的正整数')));
  } else {
    callback();
  }
};
const checkHCarSpeedMoney = (rule: any, value: number, callback: any) => {
  let pattern = /(^[1-9]\d*$)/;
  if (!dataForm.ruleConfigParam.hourObj.carSpeedMoney&&dataForm.ruleConfigParam.hourObj.carSpeedMoney!=0){
    callback(new Error(t('请填写收费规则')));
  } else if(!pattern.test(dataForm.ruleConfigParam.hourObj.carSpeedMoney.toString())){
    callback(new Error(t('请输入大于0的正整数')));
  } else {
    callback();
  }
};

const checkRebateTotal = (rule: any, value: number, callback: any) => {
  console.log(value,'value111==============')
  let rebateComMoney = dataForm.ruleConfigParam.percentObj.rebateComMoney;
  let rebateCoachMoney = dataForm.ruleConfigParam.percentObj.rebateCoachMoney;
  if(!rebateComMoney){
    callback(new Error(t('请设置考场分利')));
  }

  else if (rebateComMoney > 100 || rebateCoachMoney > 100) {
    callback(new Error(t('返利比例不能 大于 100')));
  } else {
    dataForm.ruleConfigParam.percentObj.rebateCoachMoney = 100 - value;
    callback();
  }
};

const checkHRebateTotal = (rule: any, value: number, callback: any) => {
  console.log(value,'value==============')
  let rebateComMoney = dataForm.ruleConfigParam.hourObj.rebateComMoney;
  let rebateCoachMoney = dataForm.ruleConfigParam.hourObj.rebateCoachMoney;
  let carSpeedMoney = dataForm.ruleConfigParam.hourObj.carSpeedMoney;

  if(!rebateComMoney){
    callback(new Error(t('请设置考场分利')));
  } else  if (rebateComMoney + rebateCoachMoney> carSpeedMoney) {
    callback(new Error(t('返利金额不能大于收费金额')));
  } else {
    dataForm.ruleConfigParam.hourObj.rebateCoachMoney = carSpeedMoney - value;
    callback();
  }
};

const checkRebateCoachMoney  = (rule: any, value: number, callback: any) => {
  let rebateComMoney = dataForm.ruleConfigParam.percentObj.rebateComMoney
  let rebateCoachMoney = dataForm.ruleConfigParam.percentObj.rebateCoachMoney
  if(!rebateCoachMoney&&rebateCoachMoney!=0){
    callback(new Error(t('请设置教练分利')));
  }else if (rebateComMoney > 100 || rebateCoachMoney > 100) {
    callback(new Error(t('返利比例不能 大于 100')));
  } else {
    dataForm.ruleConfigParam.percentObj.rebateComMoney = 100 - value;
    callback();
  }

};
const checkHRebateCoachMoney  = (rule: any, value: number, callback: any) => {
  let rebateComMoney = dataForm.ruleConfigParam.hourObj.rebateComMoney
  let rebateCoachMoney = dataForm.ruleConfigParam.hourObj.rebateCoachMoney
  let carSpeedMoney = dataForm.ruleConfigParam.hourObj.carSpeedMoney
  if(!rebateCoachMoney&&rebateCoachMoney!=0){
    callback(new Error(t('请设置教练分利')));
  } else {
    if (rebateComMoney+rebateCoachMoney > carSpeedMoney) {
      callback(new Error(t('返利金额不能大于收费金额')));
    } else {
      dataForm.ruleConfigParam.hourObj.rebateComMoney = carSpeedMoney - value;
      callback();
    }
  }
};

const validCarId = (rule: any, value: number, callback: any) => {
  console.log(dataList.value,'dataList.value==========')
  if(dataForm.id){return callback();}

  const list = dataList.value.filter((res:any) => {
    return res.carId == value;
  });
  if (!value) {
    return callback(new Error("请选择车牌"));
  }
  // else if (list.length === 1) {
  //   return callback(new Error("该车牌配置已存在"));
  // }
  else {
    return callback();
  }
};
const validCarType = (rule: any, value: number, callback: any) => {
  if(dataForm.id){return callback()}
  if(props.type=='1'){return callback();}
  const list = dataList.value.filter((res:any) => {
    return res.carType == value;
  });
  if (!value) {
    return callback(new Error("请选择车辆类别"));
  }
  // else if (list.length === 1) {
  //   return callback(new Error("该车辆类别配置已存在"));
  // }
  else {
    return callback();
  }
};



const getPageList = () => {
  baseService
    .get("/dj/paymentmoneycfg/page", {
      order: 'desc',
      orderField: 'create_date',
      page: 1,
      limit: 10000,
      carId:1
    })
    .then((res) => {
      dataList.value = res.data.list;
    });
};


const rules = ref({
  // ... 其他规则
  hCarSpeedMoney:[
    { validator: checkHCarSpeedMoney, required: true,trigger: 'change' }
  ],
  // rebateComMoney: [
  //   { validator: checkRebateTotal, required: true,trigger: 'change' }
  // ],
  // rebateCoachMoney: [
  //   { validator: checkRebateCoachMoney, required: true,trigger: 'change' }
  // ],
  // hRebateComMoney: [
  //   { validator: checkHRebateTotal, required: true,trigger: 'change' }
  // ],
  // hRebateCoachMoney: [
  //   { validator: checkHRebateCoachMoney, required: true,trigger: 'change' }
  // ],
  carSpeedMoney:[
    { validator: checkCarSpeedMoney, required: true,trigger: 'change' }
  ],
  carId:[
    {required: true, trigger: 'change',validator: validCarId},
  ],
  carType:[
    { validator: validCarType, required: true,trigger: 'change' }]
});

// watch(
//   () => dataForm.ruleConfigParam.percentObj.rebateComMoney,
//   (newValue) => {
//     if (dataForm.ruleConfigParam.rebateType === 1 && dataForm.ruleConfigParam.percentObj.rebateComMoney !== 0) {
//       dataForm.ruleConfigParam.percentObj.rebateCoachMoney = 100 - newValue;
//     } else if (dataForm.ruleConfigParam.rebateType === 2 && dataForm.ruleConfigParam.percentObj.carSpeedMoney !== undefined) {
//       dataForm.ruleConfigParam.percentObj.rebateCoachMoney = dataForm.ruleConfigParam.percentObj.carSpeedMoney - newValue;
//       if(dataForm.ruleConfigParam.percentObj.rebateCoachMoney<0){
//         dataForm.ruleConfigParam.percentObj.rebateCoachMoney = 0
//       }
//     }
//   },
//   { immediate: true, deep: true }
// );
// watch(
//   () => dataForm.ruleConfigParam.hourObj.rebateComMoney,
//   (newValue) => {
//     if (dataForm.ruleConfigParam.rebateType === 1 && dataForm.ruleConfigParam.hourObj.rebateComMoney !== 0) {
//       dataForm.ruleConfigParam.hourObj.rebateCoachMoney = 100 - newValue;
//     } else if (dataForm.ruleConfigParam.rebateType === 2 && dataForm.ruleConfigParam.hourObj.carSpeedMoney !== undefined) {
//       dataForm.ruleConfigParam.hourObj.rebateCoachMoney = dataForm.ruleConfigParam.hourObj.carSpeedMoney - newValue;
//       if(dataForm.ruleConfigParam.hourObj.rebateCoachMoney<0){
//         dataForm.ruleConfigParam.hourObj.rebateCoachMoney = 0
//       }
//     }
//   },
//   { immediate: true, deep: true }
// );
//
// watch(
//   () => dataForm.ruleConfigParam.percentObj.rebateCoachMoney,
//   (newValue) => {
//     if (dataForm.ruleConfigParam.rebateType === 1 && dataForm.ruleConfigParam.percentObj.rebateCoachMoney !== 0) {
//       dataForm.ruleConfigParam.percentObj.rebateComMoney = 100 - newValue;
//     } else if (dataForm.ruleConfigParam.rebateType === 2) {
//       dataForm.ruleConfigParam.percentObj.rebateComMoney = dataForm.ruleConfigParam.percentObj.carSpeedMoney - newValue;
//       if(dataForm.ruleConfigParam.percentObj.rebateComMoney<0){
//         dataForm.ruleConfigParam.percentObj.rebateComMoney = 0
//       }
//     }
//   },
//   { immediate: true, deep: true }
// );
// watch(
//   () => dataForm.ruleConfigParam.hourObj.rebateCoachMoney,
//   (newValue) => {
//     if (dataForm.ruleConfigParam.rebateType === 1 && dataForm.ruleConfigParam.hourObj.rebateCoachMoney !== 0) {
//       dataForm.ruleConfigParam.hourObj.rebateComMoney = 100 - newValue;
//     } else if (dataForm.ruleConfigParam.rebateType === 2) {
//       dataForm.ruleConfigParam.hourObj.rebateComMoney = dataForm.ruleConfigParam.hourObj.carSpeedMoney - newValue;
//       if(dataForm.ruleConfigParam.percentObj.rebateComMoney<0){
//         dataForm.ruleConfigParam.percentObj.rebateComMoney = 0
//       }
//     }
//   },
//   { immediate: true, deep: true }
// );

// 定义车辆类别映射
const carTypes = {
  1: '教练车',
  2: '测试车',
  3: '带训车',
  // 添加其他支付方式...
};


//选择客户
const addUserHandle = ()=>{
  customersignRef.value.init();
};
const handleSelectUser = (data: any)=>{
  dataForm.carId = data[0].carId;
  dataForm.carType = data[0].carType;
  dataFormRef.value.clearValidate(["carId"]);
  // dataForm.userList = JSON.parse(JSON.stringify(data));
};

const init = (id?: number) => {
  visible.value = true;
  dataForm.id = null;

  // 重置表单数据
  if (dataFormRef.value) {
    dataFormRef.value.resetFields();
  }

  // getPageList();

  if (id) {
    getInfo(id);
  }
};

// 获取信息
const getInfo = (id: number) => {
  baseService.get("/dj/paymentmoneycfg/" + id).then((res) => {
    let obj = res.data
    obj.ruleConfigParam = JSON.parse(obj.ruleConfigParam)
    Object.assign(dataForm, obj);
  });
};

// 表单提交
const dataFormSubmitHandle = () => {
  dataFormRef.value.validate((valid: boolean) => {
    if (!valid) {
      return false;
    }
    const jsonData = JSON.stringify(dataForm.ruleConfigParam);
    console.log(props.type,'props.type========')
    if(props.type=='0'){
      dataForm.carId = '0';
    }
    // 创建一个新的数据对象，包含转换后的ruleConfigParam
    const formData = { ...dataForm, ruleConfigParam: jsonData };
    (!dataForm.id ? baseService.post : baseService.put)("/dj/paymentmoneycfg", formData).then((res) => {
      ElMessage.success({
        message: t("prompt.success"),
        duration: 500,
        onClose: () => {
          visible.value = false;
          emit("refreshDataList");
        }
      });
    });
  });
};

defineExpose({
  init
});
</script>
