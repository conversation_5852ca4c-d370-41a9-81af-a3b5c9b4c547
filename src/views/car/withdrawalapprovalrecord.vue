<template>
  <div class="mod-dj__withdrawalapprovalrecord">
    <el-form :inline="true" :model="state.dataForm" @keyup.enter="state.getDataList()">
      <el-form-item>
        <el-input v-model="state.dataForm.carUserName" placeholder="用户姓名" clearable></el-input>
      </el-form-item>
      <el-form-item>
        <el-input v-model="state.dataForm.carUserPhone" placeholder="用户手机" clearable></el-input>
      </el-form-item>
      <el-form-item>
        <el-input v-model="state.dataForm.schoolName" placeholder="驾校名称" clearable></el-input>
      </el-form-item>
      <el-form-item>
        <el-button @click="state.getDataList()">{{ $t("query") }}</el-button>
      </el-form-item>
      <el-form-item>
        <el-button type="info" @click="state.exportHandle()">{{ $t("export") }}</el-button>
      </el-form-item>
      <el-form-item>
        <el-button v-if="state.hasPermission('dj:withdrawalapprovalrecord:delete')" type="danger" @click="state.deleteHandle()">{{ $t("deleteBatch") }}</el-button>
      </el-form-item>
      <el-form-item>
        <el-button v-if="state.hasPermission('dj:withdrawalapprovalrecord:check')" type="danger" @click="state.allCheckUserLimit('',false)">{{ $t("allDisagreeCheck") }}</el-button>
      </el-form-item>
      <el-form-item>
        <el-button v-if="state.hasPermission('dj:withdrawalapprovalrecord:check')" type="danger" @click="state.allCheckUserLimit('',true)">{{ $t("allAgreeCheck") }}</el-button>
      </el-form-item>
    </el-form>
    <el-table v-loading="state.dataListLoading" :data="state.dataList" border @selection-change="state.dataListSelectionChangeHandle" style="width: 100%">
      <el-table-column type="selection" header-align="center" align="center" width="50"></el-table-column>
      <el-table-column prop="createDate" label="申请时间" header-align="center" align="center"></el-table-column>
      <el-table-column prop="carUserName" label="用户姓名" header-align="center" align="center"></el-table-column>
      <el-table-column prop="carUserPhone" label="用户手机" header-align="center" align="center"></el-table-column>
      <el-table-column prop="schoolName" label="驾校名称" header-align="center" align="center"></el-table-column>
      <el-table-column prop="settAmount" label="提现金额" header-align="center" align="center"></el-table-column>
      <el-table-column prop="settAccountNo" label="提现账号" header-align="center" align="center"></el-table-column>
      <el-table-column prop="trxNo" label="转账流水号" header-align="center" align="center"></el-table-column>
      <el-table-column prop="settStatus" label="结算状态" header-align="center" align="center" :formatter="formatSettStatus"></el-table-column>
      <el-table-column prop="remake" label="备注" header-align="center" align="center"></el-table-column>
      <el-table-column prop="checkUsername" label="审核人姓名" header-align="center" align="center"></el-table-column>
      <el-table-column prop="checkTime" label="审核时间" header-align="center" align="center"></el-table-column>
      <el-table-column :label="$t('handle')" fixed="right" header-align="center" align="center" width="150">
        <template v-slot="scope">
<!--          <el-button v-if="state.hasPermission('dj:withdrawalapprovalrecord:delete')" type="primary" link @click="state.deleteHandle(scope.row.id)">{{ $t("delete") }}</el-button>-->
          <el-button v-if="state.hasPermission('dj:withdrawalapprovalrecord:check') && scope.row.settStatus === 'WAITING_CONFIRM'" type="primary" link @click="checkUserLimit(scope.row.id)">{{ $t("check") }}</el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination :current-page="state.page" :page-sizes="[10, 20, 50, 100]" :page-size="state.limit" :total="state.total" layout="total, sizes, prev, pager, next, jumper" @size-change="state.pageSizeChangeHandle" @current-change="state.pageCurrentChangeHandle"> </el-pagination>
    <!-- 弹窗, 新增 / 修改 -->
    <add-or-update :key="addKey" ref="addOrUpdateRef" @refreshDataList="state.query"></add-or-update>
  </div>
</template>

<script lang="ts" setup>
import useView from "@/hooks/useView";
import { nextTick, reactive, ref, toRefs} from "vue";
import AddOrUpdate from "./withdrawalapprovalrecord-add-or-update.vue";

const view = reactive({
  getDataListURL: "/dj/withdrawalapprovalrecord/page",
  getDataListIsPage: true,
  exportURL: "/dj/withdrawalapprovalrecord/export",
  deleteIsBatch: true,
  deleteURL: "/dj/withdrawalapprovalrecord",
  checkUserIsBatch: true,
  allCheckUserURL: "/dj/withdrawalapprovalrecord/allCheck",
  dataForm: {
    carUserName: "",
    carUserPhone: "",
    schoolName: "",
  }
});

const state = reactive({ ...useView(view), ...toRefs(view) });

//定义是否返利映射
const settStatus = {
  "WAITING_CONFIRM": "等待确认",
  "FINA_REJECT": "拒绝结算",
  "WAITING_REMIT": "审核成功，等待打款",
  "SUCCESS": "结算成功",
  "ERROR": "结算失败",
};
// 添加格式化方法
const formatSettStatus = (row, column, cellValue) => {
  return settStatus[cellValue] || '未知';
};

const addKey = ref(0);
const addOrUpdateRef = ref();

const checkUserLimit = (id?: number) => {
  addKey.value++;
  nextTick(() => {
    addOrUpdateRef.value.init(id);
  });
};

const allCheckUserLimit = (approved: boolean) => {

};
</script>
