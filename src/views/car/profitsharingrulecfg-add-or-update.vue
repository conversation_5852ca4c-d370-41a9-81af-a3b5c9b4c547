<template>
  <el-dialog v-model="visible" :title="!dataForm.id ? $t('add') : $t('update')" :close-on-click-modal="false" :close-on-press-escape="false">
    <el-form :model="dataForm" :rules="rules" ref="dataFormRef" @keyup.enter="dataFormSubmitHandle()" label-width="120px">
      <el-form-item label="车辆类别" prop="carType">
        <el-select v-model="dataForm.carType" placeholder="请选择车辆类别">
          <el-option
            v-for="(value, key) in carTypes"
            :key="key"
            :label="value"
            :value="key"
            :selected-label="value"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="考场分利比例" prop="ruleConfigParam.comMoney" :rules="rules.comMoney">
        <el-input v-model="dataForm.ruleConfigParam.comMoney" placeholder="请输入考场分利比例" :precision="0" controls-position="right" style="width: 50%"  @change="updateProfitRatio('comMoney')"/>
        <span class="margin-left-xs">%</span>
      </el-form-item>
      <el-form-item label="教练分利比例" prop="ruleConfigParam.coachMoney" :rules="rules.coachMoney">
        <el-input v-model="dataForm.ruleConfigParam.coachMoney" placeholder="请输入教练分利比例" :precision="0" controls-position="right" style="width: 50%"  @change="updateProfitRatio('coachMoney')"/>
        <span class="margin-left-xs">%</span>
      </el-form-item>
<!--      <el-form-item label="考场名称" prop="schoolName">-->
<!--        <el-select v-model="dataForm.deptId" placeholder="请选择考场名称" @change="handleSchoolChange">-->
<!--          <el-option-->
<!--            v-for="item in schoolOptions"-->
<!--            :key="item.id"-->
<!--            :label="item.name"-->
<!--            :value="item.id"-->
<!--          ></el-option>-->
<!--        </el-select>-->
<!--      </el-form-item>-->
    </el-form>
    <template v-slot:footer>
      <el-button @click="visible = false">{{ $t("cancel") }}</el-button>
      <el-button type="primary" @click="dataFormSubmitHandle()">{{ $t("confirm") }}</el-button>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { onMounted, reactive, ref } from "vue";
import baseService from "@/service/baseService";
import { useI18n } from "vue-i18n";
import { ElMessage } from "element-plus";
const { t } = useI18n();
const emit = defineEmits(["refreshDataList"]);

const visible = ref(false);
const dataFormRef = ref();

const dataForm = reactive({
  id: "",
  ruleConfigParam: {
    comMoney: 0,
    coachMoney: 0,
  },
  carType: "",
  deptId: "",
  schoolName: "",
  schoolUserId: "",
});

// 使用重构后的函数
const rules = ref({
  comMoney: createRangeValidator(0, 100, '考场分利比例必须在0-100之间', 'comMoney'),
  coachMoney: createRangeValidator(0, 100, '教练分利比例必须在0-100之间', 'coachMoney'),
});
function createRangeValidator(min, max, message, field) {
  return [
    {
      validator: (rule, value, callback) => {
        if (value < min || value > max) {
          callback(new Error(message));
        } else {
          callback();
        }
      },
      trigger: 'blur',
    },
  ];
}

onMounted(() => {
  fetchSchoolList(); // 页面加载时获取驾校列表
});

const schoolOptions = ref([]); // 用于存储驾校选项
// 定义车辆类别映射
const carTypes = {
  1: '教练车',
  2: '测试车',
  // 添加其他支付方式...
};
const disabledSchools = ref<number[]>([]);

// 更新利润比例
const updateProfitRatio = (type: 'comMoney' | 'coachMoney') => {
  const otherType = type === 'comMoney' ? 'comMoney' : 'coachMoney';
  const otherValue = dataForm.ruleConfigParam[otherType];
  dataForm.ruleConfigParam[type === 'comMoney' ? 'coachMoney' : 'comMoney'] = 100 - otherValue;
  console.log("新的值为",dataForm.ruleConfigParam[otherType]);
  console.log("100 - 新的值后等于",dataForm.ruleConfigParam[type === 'comMoney' ? 'coachMoney' : 'comMoney'] = 100 - otherValue);
};


// 获取驾校列表
const fetchSchoolList = async () => {
  try {
    const response = await baseService.get('/sys/dept/getSchoolDept'); // 假设这是获取驾校列表的接口
    schoolOptions.value = response.data.map(school => ({ id: school.deptID, name: school.name,userId: school.userId,}));
  } catch (error) {
    ElMessage.error({ message: t("prompt.errorFetchingSchoolList"), duration: 3000 });
  }
};

// 当学校名称改变时，可以在这里处理其他逻辑，比如根据学校ID获取更多信息
const handleSchoolChange = (schoolId: string) => {
  console.log("Selected school ID:", schoolId);
  if (disabledSchools.value.includes(schoolId)) {
    return; // 如果已禁用，则直接返回
  }
  // 清空之前的值以允许重新选择
  dataForm.deptId = "";
  dataForm.schoolUserId = "";
  dataForm.schoolName = "";
  // 查找选中的驾校对象
  const selectedSchool = schoolOptions.value.find(school => school.id === schoolId);
  if (selectedSchool) {
    dataForm.deptId = selectedSchool.id;
    dataForm.schoolUserId = selectedSchool.userId;
    dataForm.schoolName = selectedSchool.name;
  } else {
    console.warn("Selected school not found:", schoolId);
  }
};

const init = (id?: number) => {
  visible.value = true;
  dataForm.id = "";

  // 重置表单数据
  if (dataFormRef.value) {
    dataFormRef.value.resetFields();
  }

  if (id) {
    getInfo(id);
  }
};

// 获取信息
const getInfo = (id: number) => {
  baseService.get("/dj/profitsharingrulecfg/" + id).then((res) => {
    // 直接在res.data上删除不需要的属性
    delete res.data.updateBy;
    delete res.data.updateDate;
    delete res.data.updater;
    Object.assign(dataForm, res.data);
    const fetchedData = res.data;
    fetchedData.ruleConfigParam = JSON.parse(fetchedData.ruleConfigParam);
    fetchedData.carType = String(res.data.carType);
    Object.assign(dataForm, fetchedData);
    console.log("获取到的数据内容:",dataForm);
    // Object.assign(dataForm, res.data);
  });
};

// 表单提交
const dataFormSubmitHandle = () => {
  dataFormRef.value.validate((valid: boolean) => {
    if (!valid) {
      return false;
    }
    const jsonData = JSON.stringify(dataForm.ruleConfigParam);
    // 创建一个新的数据对象，包含转换后的ruleConfigParam
    const formData = { ...dataForm, ruleConfigParam: jsonData };
    (!dataForm.id ? baseService.post : baseService.put)("/dj/profitsharingrulecfg", formData).then((res) => {
      ElMessage.success({
        message: t("prompt.success"),
        duration: 500,
        onClose: () => {
          visible.value = false;
          emit("refreshDataList");
        }
      });
    });
  });
};

defineExpose({
  init
});
</script>
