<template>
  <el-dialog v-model="visible" :title="!dataForm.id ? $t('add') : $t('update')" :close-on-click-modal="false" :close-on-press-escape="false">
    <el-form :model="dataForm" :rules="rules" ref="dataFormRef" @keyup.enter="dataFormSubmitHandle()" label-width="120px">

      <el-form-item label="绑定模式" prop="rentalType">
        <el-select v-model="dataForm.rentalType" placeholder="请选择绑定模式">
          <el-option label="租赁测试车" :value="1"></el-option>
          <el-option label="教练员带训" :value="0"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="车主电话" prop="carUserPhone">
        <el-input v-model="dataForm.carUserPhone" placeholder="请选择车主" readonly>
          <template #append>
            <el-button :icon="Search"  @click="addUserHandle()" />
          </template>
        </el-input>
<!--        <el-button type="primary" @click="fetchOwnerInfo">{{ $t("query") }}</el-button>-->
      </el-form-item>
      <el-form-item label="车牌信息" prop="carId">
        <el-input v-model="dataForm.carId" placeholder="车牌信息" disabled></el-input>
      </el-form-item>
      <el-form-item label="车主姓名" prop="carUserName">
        <el-input v-model="dataForm.carUserName" placeholder="车主姓名" disabled></el-input>
      </el-form-item>
      <coachselect ref="customersignRef" @change="handleSelectUser"></coachselect>
    </el-form>
    <template v-slot:footer>
      <el-button @click="visible = false">{{ $t("cancel") }}</el-button>
      <el-button type="primary" @click="dataFormSubmitHandle()">{{ $t("confirm") }}</el-button>
    </template>
  </el-dialog>
  <el-dialog
    v-model="dataForm.ownersDialogVisible"
    title="选择车主"
    width="30%"
    destroy-on-close
  >
    <el-select
      v-model="selectedOwner"
      placeholder="请选择车主"
      @change="onOwnerSelect"
    >
      <el-option
        v-for="owner in dataForm.owners" :key="owner.id" :label="`${owner.username} (${owner.mobile})`" :value="owner.id">
      </el-option>
    </el-select>
    <template #footer>
    <span class="dialog-footer">
      <el-button @click="dataForm.ownersDialogVisible = false">取消</el-button>
      <el-button type="primary" @click="selectAndCloseOwnerDialog" :disabled="!selectedOwner">确定</el-button>
    </span>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { reactive, ref, onMounted} from "vue";
import baseService from "@/service/baseService";
import { useI18n } from "vue-i18n";
import { ElMessage } from "element-plus";
import Template from "@/views/devtools/template.vue";
import { Search } from "@element-plus/icons-vue";
import coachselect from "./coachselect.vue";
const { t } = useI18n();
const emit = defineEmits(["refreshDataList"]);

const visible = ref(false);
const dataFormRef = ref();
const schoolOptions = ref([]); // 用于存储驾校选项
const customersignRef = ref();

const dataForm = reactive({
  id: "",
  carType: "1",
  carUserId: "",
  carId: "",
  carUserName: "",
  carUserPhone: "",
  rentalType:'',
  deptId: "",
  schoolName: "",
  schoolUserId: "",
  selectedOwner: [], // 新增的属性，用于存储用户选择的车主
  owners: [], // 新增的属性，用于存储车主列表
  ownersDialogVisible: false, // 新增的属性，用于控制车主列表弹窗的显示隐藏
});

const rules = ref({
  // 添加对carId的验证规则
  carId: [
    { required: true, message: t('请填写车牌信息'), trigger: 'blur' }, // 必填验证
    { pattern: /^[^\s]+$/, message: t('车牌信息不能包含空格'), trigger: 'blur' },
    //{ pattern: /^[京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领A-Z]{1}[A-Z]{1}[A-Za-z0-9]{5}$/, message: t('车牌信息有误'), trigger: 'blur' } // 正则验证，确保只包含字母和数字
  ],
  selectedOwner: [
    { required: true, message: t('请选择车主信息'), trigger: 'change' },
  ],
  rentalType: [
    { required: true, message: t('请选择绑定模式'), trigger: 'blur' },
  ],
  carUserPhone: [
    {required: true, message: t('请填写车主电话'), trigger: 'blur'}
  ],
});

const selectedOwner = ref("");


// 当学校名称改变时，可以在这里处理其他逻辑，比如根据学校ID获取更多信息
const handleSchoolChange = (schoolId: string) => {
  console.log("Selected school ID:", schoolId);
  // 清空之前的值以允许重新选择
  dataForm.deptId = "";
  dataForm.schoolUserId = "";
  dataForm.schoolName = "";
  // 查找选中的驾校对象
  const selectedSchool = schoolOptions.value.find(school => school.id === schoolId);
  if (selectedSchool) {
    dataForm.deptId = selectedSchool.id;
    dataForm.schoolUserId = selectedSchool.userId;
    dataForm.schoolName = selectedSchool.name;
  } else {
    console.warn("Selected school not found:", schoolId);
  }
};

const fetchOwnerInfo = async () => {
  console.log("输入的手机号码：",dataForm.carUserPhone);
  if (!dataForm.carUserPhone.trim()) {
    ElMessage.warning({ message: t("请输入手机号"), duration: 2000 });
    return;
  }
  const params = {
    userPhone: dataForm.carUserPhone
  };
  const response = await baseService.post("/dj/carinputinfo/getCoachInfo",params);
  dataForm.owners = response.data;
  if (response.data.length === 1) { // 如果只有一个结果，直接填充信息
    dataForm.ownersDialogVisible = true;
  }else if (response.data.length > 1){
    dataForm.ownersDialogVisible = true;
  } else {
    ElMessage.error(t("请教练先扫码关注公众号进行注册"));
  }
};

const onOwnerSelect = (value) => {
  const selectedOwner = dataForm.owners.find(owner => owner.id === value);
  console.log("selectedOwner", selectedOwner);
  if (selectedOwner) {
    dataForm.selectedOwner.carUserId = selectedOwner.id;
    dataForm.selectedOwner.carUserName = selectedOwner.username;
    dataForm.selectedOwner.carUserPhone = selectedOwner.mobile;
    dataForm.selectedOwner.schoolName = selectedOwner.schoolName;
  } else {
    ElMessage.error({ message: t("请选择有效的车主信息"), duration: 2000 });
  }
};

const selectAndCloseOwnerDialog = () => {
  if (dataForm.selectedOwner) {
    console.log("sasaksasnaksndsakdsakd",dataForm.selectedOwner);
    dataForm.carUserId = dataForm.selectedOwner.carUserId;
    dataForm.carUserName = dataForm.selectedOwner.carUserName;
    dataForm.carUserPhone = dataForm.selectedOwner.carUserPhone;
    dataForm.schoolName = dataForm.selectedOwner.schoolName;
    dataForm.ownersDialogVisible = false;
  } else {
    ElMessage.error({ message: t("请选择有效的车主信息"), duration: 2000 });
  }
};

// 定义车辆类别映射
const carTypes = {
  1: '教练车',
  2: '测试车',
  // 添加其他支付方式...
};

const init = (id?: number) => {
  console.log("阿莎撒结合实际撒娇");
  visible.value = true;
  dataForm.id = "";

  // 重置表单数据
  if (dataFormRef.value) {
    dataFormRef.value.resetFields();
  }

  if (id) {
    getInfo(id);
  }
};

// 获取信息
const getInfo = (id: number) => {
  baseService.get("/dj/carinputinfo/" + id).then((res) => {
    Object.assign(dataForm, res.data);
  });
};
//选择客户
const addUserHandle = ()=>{
  customersignRef.value.init();
};
const handleSelectUser = (data: any)=>{
  dataForm.owners = data[0];
  dataForm.carUserPhone = data[0].mobile;
  dataForm.carUserId = data[0].id; // 清空车主ID
  dataForm.carUserName = data[0].username; // 清空车主姓名
  dataForm.schoolName = data[0].schoolName; // 如果适用，清空考场名称
  dataFormRef.value.clearValidate(["carUserPhone"]);
  // dataForm.userList = JSON.parse(JSON.stringify(data));
};

// 表单提交
const dataFormSubmitHandle = () => {
  dataFormRef.value.validate((valid: boolean) => {
    if (!valid) {
      return false;
    }
    baseService.post("/dj/carexaminationrentalrecord", dataForm).then((res) => {
      ElMessage.success({
        message: t("prompt.success"),
        duration: 500,
        onClose: () => {
          visible.value = false;
          emit("refreshDataList");
        }
      });
    });
  });
};

defineExpose({
  init
});
</script>
