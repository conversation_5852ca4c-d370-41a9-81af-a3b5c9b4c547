<template>
  <el-dialog v-model="visible" :title="!dataForm.id ? $t('add') : $t('update')" :close-on-click-modal="false" :close-on-press-escape="false">
    <el-form :model="dataForm" :rules="rules" ref="dataFormRef" @keyup.enter="dataFormSubmitHandle()" label-width="120px">
      <el-form-item label="预约日期" prop="bookingTime">
        <el-input v-model="dataForm.bookingTime" placeholder="预约日期"></el-input>
      </el-form-item>
      <el-form-item label="教练用户姓名" prop="carUserName">
        <el-input v-model="dataForm.carUserName" placeholder="教练用户姓名"></el-input>
      </el-form-item>
      <el-form-item label="学员姓名" prop="studentName">
        <el-input v-model="dataForm.studentName" placeholder="学员姓名"></el-input>
      </el-form-item>
      <el-form-item label="支付时间" prop="updateDate">
        <el-input v-model="dataForm.updateDate" placeholder="支付时间"></el-input>
      </el-form-item>
    </el-form>
    <template v-slot:footer>
      <el-button @click="visible = false">{{ $t("cancel") }}</el-button>
      <el-button type="primary" @click="dataFormSubmitHandle()">{{ $t("confirm") }}</el-button>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { reactive, ref } from "vue";
import baseService from "@/service/baseService";
import { useI18n } from "vue-i18n";
import { ElMessage } from "element-plus";
const { t } = useI18n();
const emit = defineEmits(["refreshDataList"]);

const visible = ref(false);
const dataFormRef = ref();

const dataForm = reactive({
  id: "",
  bookingCfgId: "",
  bookingTime: "",
  orderId: "",
  orderMoney: "",
  carType: "",
  carUserId: "",
  carUserName: "",
  carUserPhone: "",
  schoolName: "",
  studentName: "",
  studentIdCard: "",
  studentPhone: "",
  payUserOpenId: "",
  deptId: "",
  companyUserId: "",
  outTradeNo: "",
  creator: "",
  wxPayNotify: "",
  createBy: "",
  createDate: "",
  updater: "",
  updateBy: "",
  updateDate: "",
  shareMoney: "",
});

const rules = ref({
});

const init = (id?: number) => {
  visible.value = true;
  dataForm.id = "";

  // 重置表单数据
  if (dataFormRef.value) {
    dataFormRef.value.resetFields();
  }

  if (id) {
    getInfo(id);
  }
};

// 获取信息
const getInfo = (id: number) => {
  baseService.get("/dj/exambookingorderinfo/" + id).then((res) => {
    Object.assign(dataForm, res.data);
  });
};

// 表单提交
const dataFormSubmitHandle = () => {
  dataFormRef.value.validate((valid: boolean) => {
    if (!valid) {
      return false;
    }
    (!dataForm.id ? baseService.post : baseService.put)("/dj/exambookingorderinfo", dataForm).then((res) => {
      ElMessage.success({
        message: t("prompt.success"),
        duration: 500,
        onClose: () => {
          visible.value = false;
          emit("refreshDataList");
        }
      });
    });
  });
};

defineExpose({
  init
});
</script>