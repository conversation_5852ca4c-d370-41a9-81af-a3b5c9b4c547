<template>
  <div class="mod-dj__paymentmoneycfg">
    <el-form :inline="true" :model="state.dataForm" @keyup.enter="state.getDataList()">
      <el-form-item>
        <el-select clearable v-model="state.dataForm.carType" placeholder="车辆类别">
          <el-option
            v-for="(value, key) in carTypes"
            :key="key"
            :label="value"
            :value="key"
          ></el-option>
        </el-select>
      </el-form-item>
            <el-form-item v-if="configType==1">
              <el-input v-model="state.carId" placeholder="车牌" clearable></el-input>
            </el-form-item>
      <el-form-item>
        <el-button @click="handleSearchList">{{ $t("query") }}</el-button>
      </el-form-item>
      <el-form-item>
        <el-button type="info" @click="state.exportHandle()">{{ $t("export") }}</el-button>
      </el-form-item>
      <el-form-item>
        <el-button v-if="state.hasPermission('dj:paymentmoneycfg:save')" type="primary" @click="addOrUpdateHandle()">{{ $t("add") }}</el-button>
      </el-form-item>
      <el-form-item>
        <el-button v-if="state.hasPermission('dj:paymentmoneycfg:delete')" type="danger" @click="state.deleteHandle()">{{ $t("deleteBatch") }}</el-button>
      </el-form-item>
    </el-form>
    <el-table v-loading="state.dataListLoading" :data="state.dataList" border @selection-change="state.dataListSelectionChangeHandle" @sort-change="state.dataListSortChangeHandle" style="width: 100%">
      <el-table-column type="selection" header-align="center" align="center" width="50"></el-table-column>
      <el-table-column prop="carType" label="车辆类别" header-align="center" align="center" :formatter="formatCarType"></el-table-column>
      <el-table-column prop="carId" label="车牌" header-align="center" align="center" v-if="props.configType==1"></el-table-column>
      <el-table-column prop="ruleConfigParam" label="规则描述" header-align="center" align="center" width="500">
        <template v-slot="scope">
          <span v-if="scope.row.ruleConfigParam && isJsonString(scope.row.ruleConfigParam)">
            {{ formatRuleDescription(scope.row) }}
          </span>
          <span v-else>无效的规则描述</span>
        </template>
      </el-table-column>
      <!--      <el-table-column prop="examinationRoomName" label="考场名称" header-align="center" align="center"></el-table-column>-->
      <el-table-column prop="cfgStatus" label="规则状态" header-align="center" align="center"></el-table-column>
      <el-table-column prop="createBy" label="创建人" header-align="center" align="center"></el-table-column>
      <el-table-column prop="createDate" label="创建时间" sortable="custom" header-align="center" align="center"></el-table-column>
      <el-table-column :label="$t('handle')" fixed="right" header-align="center" align="center" width="150">
        <template v-slot="scope">
          <el-button v-if="state.hasPermission('dj:paymentmoneycfg:update')" type="primary" link @click="addOrUpdateHandle(scope.row.id)">{{ $t("update") }}</el-button>
          <el-button v-if="state.hasPermission('dj:paymentmoneycfg:delete')" type="primary" link @click="state.deleteHandle(scope.row.id)">{{ $t("delete") }}</el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination :current-page="state.page" :page-sizes="[10, 20, 50, 100]" :page-size="state.limit" :total="state.total" layout="total, sizes, prev, pager, next, jumper" @size-change="state.pageSizeChangeHandle" @current-change="state.pageCurrentChangeHandle"> </el-pagination>
    <!-- 弹窗, 新增 / 修改 -->
    <add-or-update :type="configType" :key="addKey" ref="addOrUpdateRef" @refreshDataList="state.getDataList"></add-or-update>
  </div>
</template>

<script lang="ts" setup>
import useView from "@/hooks/useView";
import { nextTick, reactive, ref, toRefs, watch } from "vue";
import AddOrUpdate from "./paymentmoneycfg-add-or-update.vue";
const props = defineProps({
  configType: Number
});
console.log(props.configType)
const view = reactive({
  getDataListURL: "/dj/paymentmoneycfg/page",
  getDataListIsPage: true,
  exportURL: "/dj/paymentmoneycfg/export",
  deleteURL: "/dj/paymentmoneycfg",
  deleteIsBatch: true,
  dataForm: {
    carId:props.configType==0?'0':'1',
    carType: "",
    examinationRoomName: "",
  },
  carId:''
});

const state = reactive({ ...useView(view), ...toRefs(view) });

// 新增的方法，检查字符串是否为有效的 JSON
function isJsonString(str) {
  try {
    JSON.parse(str);
    return true;
  } catch (e) {
    return false;
  }
}

// 格式化收费规则描述
function formatRuleDescription(row) {
  const config = JSON.parse(row.ruleConfigParam);
  console.log(config.hourObj)
  if (!config || (!config.hourObj.carSpeedMoney && !config.percentObj.carSpeedMoney)) return '无效的规则描述';

  let chargeDesc = '';
  let rebateDesc = '';

  // 添加收费类型描述
  if (config.chargeType === 1) {
    chargeDesc = '每小时 ';
  } else if (config.chargeType === 2) {
    chargeDesc = '每天 ';
  } else {
    chargeDesc = '未知收费周期 ';
  }

  // 添加返利类型描述
  // if (config.rebateType === 1) {
  //   const rebateComPercent = parseInt(config.percentObj.rebateComMoney) / 100;
  //   const rebateCoachPercent = parseInt(config.percentObj.rebateCoachMoney) / 100;
  //   rebateDesc = `，公司返利${rebateComPercent * 100}%，教练返利${rebateCoachPercent * 100}%`;
  // } else if (config.rebateType === 2) {
  //   rebateDesc = `，公司返利${config.hourObj.rebateComMoney}元/小时，教练返利${config.hourObj.rebateCoachMoney}元/小时`;
  // } else {
  //   rebateDesc = '，未知返利方式';
  // }

  return `${chargeDesc}${config.rebateType === 2?config.hourObj.carSpeedMoney:config.percentObj.carSpeedMoney} 元，不足1小时按1小时收费`;
}

// 定义车辆类别映射
const carTypes = {
  1: '教练车',
  2: '测试车',
  3: '带训车',
  // 添加其他支付方式...
};
// 添加格式化方法
const formatCarType = (row, column, cellValue) => {
  return carTypes[cellValue] || '未知';
};

const addKey = ref(0);
const addOrUpdateRef = ref();
const addOrUpdateHandle = (id?: number) => {
  addKey.value++;
  nextTick(() => {
    addOrUpdateRef.value.init(id);
  });
};

const handleSearchList = () => {
  if(state.carId){
    state.dataForm.carId = state.carId;
  } else {
    state.dataForm.carId = props.configType==0?'0':'1';
  }
  state.getDataList()
}
</script>
