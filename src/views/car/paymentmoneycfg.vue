<template>
  <div>
    <el-row :gutter="20">
      <el-col :span="24">
<!--        <div class="t-header margin-bottom"><span class="line"></span>收费规则配置</div>-->
        <el-tabs v-model="activeName" class="demo-tabs" >
          <el-tab-pane label="标准设置" name="first">
            <paymentmoneycfgCommon ref="standardList" :configType="0"></paymentmoneycfgCommon>
          </el-tab-pane>
<!--          <el-tab-pane label="个性化设置" name="second">-->
<!--            <paymentmoneycfgCommon ref="channelList"  :configType="1"></paymentmoneycfgCommon>-->
<!--          </el-tab-pane>-->
        </el-tabs>
      </el-col>
    </el-row>
  </div>
</template>

<script lang="ts">
import { defineComponent, reactive, toRefs, nextTick } from "vue";
import paymentmoneycfgCommon from "./paymentmoneycfgCommon.vue";
export default defineComponent({
  components: {
    paymentmoneycfgCommon
  },
  setup() {
    const state = reactive({
      dataListLoading: false,
      activeName: "first",
    });
    return { ...toRefs(state) };
  },
  methods: {
    handleChange(event) {
      console.log(event);
    }
  }
});
</script>
<style lang="less" scoped>
.line {
  display: inline-block;
  width: 6px;
  height: 20px;
  background: #409eff;
  margin-right: 5px;
  vertical-align: bottom;
  font-weight: bold;
}
.common-form {
  .el-input {
    width: 200px;
  }
  /deep/.el-radio__input.is-checked + .el-radio__label {
    color: #000 !important;
  }
}
</style>
