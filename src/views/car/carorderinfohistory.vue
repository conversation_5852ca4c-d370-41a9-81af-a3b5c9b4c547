<template>
  <div class="mod-dj__carorderinfohistory">
    <el-form :inline="true" :model="state.dataForm" @keyup.enter="state.getDataList()">
      <el-form-item>
        <el-input v-model="state.dataForm.carOrderInfoId" placeholder="订单ID" clearable></el-input>
      </el-form-item>
      <el-form-item>
        <el-input v-model="state.dataForm.carId" placeholder="车牌信息" clearable></el-input>
      </el-form-item>
      <el-form-item>
        <el-input v-model="state.dataForm.carUserName" placeholder="车主姓名" clearable></el-input>
      </el-form-item>
      <el-form-item>
        <el-input v-model="state.dataForm.carUserPhone" placeholder="车主电话" clearable></el-input>
      </el-form-item>
      <el-form-item>
        <el-select v-model="state.dataForm.carType" placeholder="请选择车辆类别" clearable>
          <el-option
            v-for="(value, key) in carTypes"
            :key="key"
            :label="value"
            :value="key"
            :selected-label="value"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="">
        <el-date-picker
          v-model="state.warehousingDate"
          type="datetimerange"
          format="YYYY-MM-DD HH:mm:ss"
          value-format="YYYY-MM-DD HH:mm:ss"
          :default-time="new Date()"
          start-placeholder="开始时间"
          end-placeholder="结束时间"></el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button @click="handleSearch">{{ $t("query") }}</el-button>
      </el-form-item>
      <el-form-item>
        <el-button type="info" @click="state.exportHandle()">{{ $t("export") }}</el-button>
      </el-form-item>
<!--      <el-form-item>-->
<!--        <el-button v-if="state.hasPermission('dj:carorderinfohistory:delete')" type="danger" @click="state.deleteHandle()">{{ $t("deleteBatch") }}</el-button>-->
<!--      </el-form-item>-->
    </el-form>
    <el-table v-loading="state.dataListLoading" :data="state.dataList" border @selection-change="state.dataListSelectionChangeHandle" style="width: 100%">
      <el-table-column type="selection" header-align="center" align="center" width="50"></el-table-column>
      <el-table-column prop="carOrderInfoId" label="订单id" header-align="center" align="center" show-overflow-tooltip></el-table-column>
      <el-table-column prop="carId" label="车牌信息" header-align="center" align="center"></el-table-column>
      <el-table-column prop="carUserName" label="车主姓名" header-align="center" align="center"></el-table-column>
      <el-table-column prop="carUserPhone" label="车主电话" header-align="center" align="center"></el-table-column>
      <el-table-column prop="carType" label="车辆类别" header-align="center" align="center" :formatter="formatCarType"></el-table-column>
      <el-table-column prop="orderMoney" label="订单金额" header-align="center" align="center">
        <template v-slot="scope">
          <span>{{ scope.row.orderMoney }}元</span>
        </template>
      </el-table-column>
      <el-table-column prop="shareMoney" label="教练收益" header-align="center" align="center">
        <template v-slot="scope">
          <span>{{ scope.row.payStatus!=2?(scope.row.shareMoney?scope.row.shareMoney:0)+'元':'--'   }}</span>
        </template>
      </el-table-column>
<!--      <el-table-column prop="payOrderMoney" label="订单支付金额" header-align="center" align="center"></el-table-column>-->
<!--      <el-table-column prop="shareMoney" label="返利金额" header-align="center" align="center"></el-table-column>-->
      <el-table-column prop="schoolName" label="驾校名称" header-align="center" align="center"></el-table-column>
<!--      <el-table-column prop="rentalType" label="租借类型" header-align="center" align="center"></el-table-column>-->
      <el-table-column prop="trainerName" label="带训员姓名" header-align="center" align="center"></el-table-column>
      <el-table-column prop="warehousingDate" label="开始时间" header-align="center" align="center"></el-table-column>
      <el-table-column prop="outboundDate" label="结束时间" header-align="center" align="center"></el-table-column>
      <el-table-column prop="remake" label="备注" header-align="center" align="center"></el-table-column>
      <el-table-column prop="createBy" label="创建人" header-align="center" align="center"></el-table-column>
      <el-table-column prop="createDate" label="创建时间" header-align="center" align="center"></el-table-column>
      <el-table-column :label="$t('handle')" fixed="right" header-align="center" align="center" width="150">
        <template v-slot="scope">
<!--          <el-button v-if="state.hasPermission('dj:carorderinfohistory:update')" type="primary" link @click="addOrUpdateHandle(scope.row.id)">{{ $t("update") }}</el-button>-->
          <el-button v-if="state.hasPermission('dj:carorderinfohistory:delete')" type="primary" link @click="state.deleteHandle(scope.row.id)">{{ $t("delete") }}</el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination :current-page="state.page" :page-sizes="[10, 20, 50, 100]" :page-size="state.limit" :total="state.total" layout="total, sizes, prev, pager, next, jumper" @size-change="state.pageSizeChangeHandle" @current-change="state.pageCurrentChangeHandle"> </el-pagination>
    <!-- 弹窗, 新增 / 修改 -->
    <add-or-update :key="addKey" ref="addOrUpdateRef" @refreshDataList="state.getDataList"></add-or-update>
  </div>
</template>

<script lang="ts" setup>
import useView from "@/hooks/useView";
import { nextTick, reactive, ref, toRefs, watch } from "vue";
import AddOrUpdate from "./carorderinfohistory-add-or-update.vue";

const view = reactive({
  getDataListURL: "/dj/carorderinfohistory/page",
  getDataListIsPage: true,
  exportURL: "/dj/carorderinfohistory/export",
  deleteURL: "/dj/carorderinfohistory",
  deleteIsBatch: true,
  dataForm: {
    carOrderInfoId: "",
    carId: "",
    carUserName: "",
    carUserPhone: "",
    carType: "",
    startTime: "",
    endTime: "",
  },
  warehousingDate:''
});

const state = reactive({ ...useView(view), ...toRefs(view) });

// 定义车辆类别映射
const carTypes = {
  1: '教练车',
  2: '测试车',
  3: '带训车',
  // 添加车辆类别...
};
// 添加格式化方法
const formatCarType = (row, column, cellValue) => {
  return carTypes[cellValue] || '未知';
};
const addKey = ref(0);
const addOrUpdateRef = ref();
const addOrUpdateHandle = (id?: number) => {
  addKey.value++;
  nextTick(() => {
    addOrUpdateRef.value.init(id);
  });
};

const handleSearch = () => {
  if(state.warehousingDate){
    state.dataForm.startTime = state.warehousingDate[0];
    state.dataForm.endTime = state.warehousingDate[1];
  } else {
    state.dataForm.startTime = '';
    state.dataForm.endTime = '';
  }
  state.getDataList();
};
</script>
